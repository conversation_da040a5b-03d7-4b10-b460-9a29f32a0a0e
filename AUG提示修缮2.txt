﻿系统设置原备份文件是可以正常导入数据的，该备份数据是从系统备份的。现在会出现以下错误:
settings.html:534 导入数据失败: Error: 导入的数据格式不正确
    at reader.onload (settings.html:510:35)

试驾车型 增加“列设置”参考“客户管理”的列设置样式功能
实时更新：用户在列设置弹窗中勾选/取消勾选后，点击"应用设置"时表格立即更新显示
- **状态同步**：确保localStorage中保存的设置与实际显示的列完全一致
- **错误处理**：添加必要的错误处理，确保设置应用失败时有适当提示

导出功能还是没有按需求的格式支持扩展
- **涉及模块**：
- 展厅录入模块
- 线索录入模块 
- 订单管理模块
- **当前问题**：导出功能格式支持不完整
- **实现要求**：
- 为所有三个模块的导入功能添加CSV格式支持
- 为所有三个模块的导入功能添加Excel格式支持
- 确保导出的文件格式正确，数据完整
- 在导入界面提供格式选择选项（CSV/Excel）
- 参考库存管理中的导出功能模块，保持与现有导入功能的界面风格一致
customerModule.js:1738 导入失败: TypeError: data.split is not a function
    at Object.read (xlsx.full.min.js:146:30)
    at reader.onload (customerModule.js:1750:43)


customers.html
database.js:1692 数据库模块已加载
customerModule.js:2214 Uncaught SyntaxError: Unexpected token ')'
customers.html:363 初始化失败: Error: 客户模块未加载
    at HTMLDocument.<anonymous> (customers.html:352:27)
(anonymous) @ customers.html:363

试驾车型列设置：
Uncaught ReferenceError: showModal is not defined
showTestDriveModelsColumnSettings @ settingsModule.js:2054
onclick @ settings.html:1

订单管理的编辑（图标）隐藏起来，在查看中已有相同功能。
订单管理，使用导入，会出现部分字段无法正常识别导入:
选装件会出现只能识别第一个选项，如果有第二个选项就识别不到
交付日期无法识别导入数据
定金、合同价能识别但部分行不正确识别

订单管理表格中"订单号"，命名格式：YYMMDDXXX001（年月日+销售顾问姓名拼音缩写+3位递增序号），确保每个订单号都是唯一性。
settings.html中的各板块的编辑、删除，保留图表，去除文字描述。
系统设置，导入数据会出现渠道管理、来店类型、试驾车型无数据导入，检查是否备份数据没有备份到上述的数据。
展厅录入-导入出现错误
customerModule.js:1846 导入行失败: Error: 客户名称和电话为必填字段
    at customerModule.js:1693:27
    at Object.importFromCSV (customerModule.js:1836:25) Object
线索录入-导入出现错误
importFromCSV @ customerModule.js:1846
customerModule.js:1846 导入行失败: Error: 客户名称和电话为必填字段
    at validateFunction (customerModule.js:2198:23)
    at Object.importFromCSV (customerModule.js:1836:25) {录入日期: '2025-07-26', 是否有效: '是', 客户名称: '王线索', 电话: '13800138000', 线索ID: '123123', …}
importFromCSV @ customerModule.js:1846
await in importFromCSV
processLeadsImport @ customerModule.js:2210
confirmBtn.onclick @ customerModule.js:2184


订单管理、订单审核、配车交付在进行状态操作时，其他数据会出现状态变更的现象。请确保每一项操作只对该数据生效。
订单管理字段：订单号、审核状态、订单日期、客户名称、联系手机、销售顾问、VIN、车型、配置、外色、内饰、选装件、交付日期、定金、合同价
订单管理列设置调整的字段要与订单管理的字段内容一致。
展厅录入使用导入功能导入数据会出现录入日期、销售顾问、滞店时间、意向、意向车型、金融、置换、报价的信息无法准确导入或无法显示该内容。
线索录入使用导入功能导入数据会出现意向车型、区域、微信、渠道、到店日期、成交日期、转销售跟进、接待顾问、首次跟进日期、跟进情况的信息无法准确导入或无法显示该内容。
settings.html中的渠道管理、来店类型、试驾车型，没有正确备份到该数据,缺失这些板块的数据备份。
settings.html中的各板块的编辑、删除样式参考“线索渠道”的样式。

新增订单表单“序号”已经更改为“订单号”请检查是否还有为更改的地方。
订单管理导入订单数据后会在字段产生“序号”列，请删除该内容。
订单管理板块不需要“序号”功能
订单管理中的"订单号"，命名格式：YYMMDDXXX001（年月日+对应销售顾问姓名的拼音缩写+2位递增序号），确保每个订单号都是唯一性。

展厅录入，导出字段为：录入日期、录入人员、销售顾问、来店时间、离店时间、滞店时间、来店类型、来店渠道、客户名称、性别、电话、意向车型、意向、区域、现有车型、对比车型、金融、置换、试驾、备注
操作导入按键，会出现：来店渠道、性别、意向车型、现有车型、对比车型、置换、试驾、备注的内容没有被正确导入。请检查并修改，并检查线索录入的导出、导入功能是否也有同意的错误。
展厅录入列表、线索录入列表页面的显示为50条，超出部分翻页。


请修复展厅录入和线索录入功能的导入导出问题，并优化相关功能。具体要求如下：

**展厅录入模块问题修复：**
1. **导出功能完整性修复**：确保导出功能包含以下所有字段（按顺序）：
   - 录入日期、录入人员、销售顾问、来店时间、离店时间、滞店时间
   - 来店类型、来店渠道、客户名称、性别、电话
   - 意向车型、意向、区域、现有车型、对比车型
   - 金融、置换、试驾、备注

2. **导入功能字段映射修复**：以下字段当前无法正确导入，需要修复字段映射和数据处理逻辑：
   - 来店渠道（下拉选择字段）
   - 性别（男/女选择字段）
   - 意向车型（车型选择字段）
   - 现有车型（车型选择字段）
   - 对比车型（车型选择字段）
   - 置换（是/否选择字段）
   - 试驾（是/否选择字段）
   - 备注（文本字段）

**线索录入模块检查与修复：**
3. **全面检查线索录入模块**：
   - 检查导出功能是否包含所有必要字段
   - 检查导入功能是否存在字段映射问题
   - 如发现与展厅录入相同的问题，请一并修复

**功能增强：**
4. **分页功能实现**：
   - 展厅录入列表页面：实现分页显示，每页50条记录
   - 线索录入列表页面：实现分页显示，每页50条记录
   - 包含页码导航、总记录数显示、跳转到指定页功能

5. **线索录入表单字段增加**：
   - 在"是否有效"字段后面添加"是否重复"字段（是/否选择）
   - 需要同步更新：表单界面、数据库表结构、显示列表、导入导出功能

**技术实现要求：**
- 修复导入导出功能的字段映射关系，确保数据类型匹配
- 实现数据验证机制，确保导入数据的完整性和格式正确性
- 分页功能需要支持前端展示和后端数据查询优化
- 数据库变更需要提供迁移脚本
- 提供完整的测试用例，验证所有修复功能正常工作

**验收标准：**
- 导入导出功能能够正确处理所有字段
- 分页功能正常工作，性能良好
- 新增字段在所有相关功能中正常显示和操作
- 所有修改不影响现有数据和功能



所有页面板块涉及到日期显示的，格式统一为：YYYY/M/DD
线索录入：是否有效，改名有效；是否重复，重复
展厅录入：金融、置换导入的数据，显示的是英文，要中文显示。
线索录入：微信导入的数据，显示的是英文，要中文显示。
订单管理的导出，要有全选、全不选，参考展厅录入的导出设计。
入库管理，导出错误
partsModule.js:2221 导出失败: TypeError: this.showModal is not a function
    at Object.exportInboundToExcel (partsModule.js:2202:18)
    at HTMLButtonElement.onclick (parts.html:1:16)
exportInboundToExcel @ partsModule.js:2221
入库管理，导入错误
partsModule.js:2858 解析Excel文件失败: TypeError: data.split is not a function
    at Object.read (xlsx.full.min.js:146:30)
    at reader.onload (partsModule.js:2791:43)

首次再次客流分析
首次：对应展厅录入中来店类型FU、FUD
首次邀约：对应展厅录入中的来店类型FUI、FUID
显示数据：当日、当月、全年累计，对应数据的月度环比

车型分析
车型意向：对应展厅录入中来店类型FU、FUI、FUD、FUID、BB、BBI、BBD、BBID、保有对应的数量
显示数据：当日、当月、全年累计
车型试驾率：对应展厅录入中来店类型FU、FUI、FUD、FUID、BB、BBI、BBD、BBID、保有对应的意向车型数量
显示数据：当日、当月、全年累计
大板块有筛选，各小板块图表有筛选，是否有更好的优化建议？
订单排名
各销售顾问订单数统计
月份、年份筛选
各销售顾问交车数统计
月份、年份筛选
