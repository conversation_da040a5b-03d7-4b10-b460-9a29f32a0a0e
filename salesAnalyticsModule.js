// 销售分析模块
window.salesAnalytics = {
    // 缓存数据
    cachedData: {
        customers: [],
        showroomEntries: [],
        leadEntries: [],
        orders: [],
        deliveries: [],
        inventory: [],
        targets: {
            delivery: [],
            order: [],
            retail: []
        }
    },

    // 初始化销售分析模块
    init: async function() {
        try {
            await this.loadAllData();
            this.renderAnalytics();
            this.bindEvents();
        } catch (error) {
            console.error('销售分析模块初始化失败:', error);
            throw error;
        }
    },

    // 加载所有数据
    loadAllData: async function() {
        try {
            // 并行加载所有数据
            const [
                customers,
                showroomEntries,
                leadEntries,
                orders,
                deliveries,
                inventory,
                deliveryTargets,
                orderTargets,
                retailTargets
            ] = await Promise.all([
                window.dbFunctions.getAllCustomerManagement(),
                window.dbFunctions.getAllShowroomEntries(),
                window.dbFunctions.getAllLeadEntries(),
                window.dbFunctions.getAllOrderManagement(),
                window.dbFunctions.getAllDeliveryManagement(),
                window.dbFunctions.getAllInventoryManagement(),
                window.dbFunctions.getAllDeliveryTargets(),
                window.dbFunctions.getAllOrderTargets(),
                window.dbFunctions.getAllRetailTargets()
            ]);

            this.cachedData = {
                customers,
                showroomEntries,
                leadEntries,
                orders,
                deliveries,
                inventory,
                targets: {
                    delivery: deliveryTargets,
                    order: orderTargets,
                    retail: retailTargets
                }
            };

            console.log('销售分析数据加载完成:', this.cachedData);
        } catch (error) {
            console.error('加载销售分析数据失败:', error);
            throw error;
        }
    },

    // 获取销售漏斗数据
    getSalesFunnelData: function() {
        const { leadEntries, showroomEntries, orders, deliveries } = this.cachedData;
        
        // 统计各阶段数量
        const leads = leadEntries.filter(entry => entry.isValid).length;
        const showroomVisits = showroomEntries.length;
        const testDrives = showroomEntries.filter(entry => entry.testDrive === '是').length;
        const ordersCount = orders.length;
        const deliveriesCount = deliveries.filter(delivery => delivery.orderStatus === '已交付').length;

        return {
            leads,
            showroomVisits,
            testDrives,
            orders: ordersCount,
            deliveries: deliveriesCount,
            conversion: {
                leadsToShowroom: showroomVisits > 0 ? ((showroomVisits / leads) * 100).toFixed(1) : 0,
                showroomToTestDrive: testDrives > 0 ? ((testDrives / showroomVisits) * 100).toFixed(1) : 0,
                testDriveToOrder: ordersCount > 0 ? ((ordersCount / testDrives) * 100).toFixed(1) : 0,
                orderToDelivery: deliveriesCount > 0 ? ((deliveriesCount / ordersCount) * 100).toFixed(1) : 0
            }
        };
    },

    // 获取月度销售趋势数据
    getMonthlySalesTrend: function(months = 12) {
        const { orders, deliveries } = this.cachedData;
        const now = new Date();
        const monthlyData = [];

        for (let i = months - 1; i >= 0; i--) {
            const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            
            const monthOrders = orders.filter(order => {
                const orderDate = new Date(order.orderDate);
                return orderDate.getFullYear() === date.getFullYear() && 
                       orderDate.getMonth() === date.getMonth();
            });

            const monthDeliveries = deliveries.filter(delivery => {
                if (!delivery.deliveryDate) return false;
                const deliveryDate = new Date(delivery.deliveryDate);
                return deliveryDate.getFullYear() === date.getFullYear() && 
                       deliveryDate.getMonth() === date.getMonth();
            });

            monthlyData.push({
                month: monthKey,
                monthName: `${date.getFullYear()}年${date.getMonth() + 1}月`,
                orders: monthOrders.length,
                deliveries: monthDeliveries.length,
                revenue: monthDeliveries.reduce((sum, delivery) => {
                    const order = orders.find(o => o.serialNumber === delivery.serialNumber);
                    return sum + (order ? parseFloat(order.totalPrice || 0) : 0);
                }, 0)
            });
        }

        return monthlyData;
    },

    // 获取客户来源分析数据
    getCustomerSourceAnalysis: function() {
        const { leadEntries, showroomEntries } = this.cachedData;
        const sourceData = {};

        // 统计线索来源
        leadEntries.forEach(entry => {
            const source = entry.channel || '未知';
            if (!sourceData[source]) {
                sourceData[source] = { leads: 0, showroom: 0, conversion: 0 };
            }
            sourceData[source].leads++;
        });

        // 统计展厅来访渠道
        showroomEntries.forEach(entry => {
            const source = entry.channel || '未知';
            if (!sourceData[source]) {
                sourceData[source] = { leads: 0, showroom: 0, conversion: 0 };
            }
            sourceData[source].showroom++;
        });

        // 计算转化率
        Object.keys(sourceData).forEach(source => {
            const data = sourceData[source];
            data.conversion = data.leads > 0 ? ((data.showroom / data.leads) * 100).toFixed(1) : 0;
        });

        return sourceData;
    },

    // 获取销售顾问业绩数据
    getSalesAdvisorPerformance: function() {
        const { orders, deliveries, showroomEntries } = this.cachedData;
        const advisorData = {};

        // 统计订单数据
        orders.forEach(order => {
            const advisor = order.salesAdvisor || '未知';
            if (!advisorData[advisor]) {
                advisorData[advisor] = {
                    orders: 0,
                    deliveries: 0,
                    revenue: 0,
                    showroomVisits: 0
                };
            }
            advisorData[advisor].orders++;
            advisorData[advisor].revenue += parseFloat(order.totalPrice || 0);
        });

        // 统计交付数据
        deliveries.forEach(delivery => {
            if (delivery.orderStatus === '已交付') {
                const order = orders.find(o => o.serialNumber === delivery.serialNumber);
                if (order) {
                    const advisor = order.salesAdvisor || '未知';
                    if (advisorData[advisor]) {
                        advisorData[advisor].deliveries++;
                    }
                }
            }
        });

        // 统计展厅接待数据
        showroomEntries.forEach(entry => {
            const advisor = entry.salesAdvisor || '未知';
            if (!advisorData[advisor]) {
                advisorData[advisor] = {
                    orders: 0,
                    deliveries: 0,
                    revenue: 0,
                    showroomVisits: 0
                };
            }
            advisorData[advisor].showroomVisits++;
        });

        return advisorData;
    },

    // 获取车型销售分析数据
    getVehicleModelAnalysis: function() {
        const { orders, deliveries, inventory } = this.cachedData;
        const modelData = {};

        // 统计订单中的车型
        orders.forEach(order => {
            const model = order.vehicleModel || '未知';
            if (!modelData[model]) {
                modelData[model] = {
                    orders: 0,
                    deliveries: 0,
                    inventory: 0,
                    revenue: 0
                };
            }
            modelData[model].orders++;
            modelData[model].revenue += parseFloat(order.totalPrice || 0);
        });

        // 统计交付中的车型
        deliveries.forEach(delivery => {
            if (delivery.orderStatus === '已交付') {
                const order = orders.find(o => o.serialNumber === delivery.serialNumber);
                if (order) {
                    const model = order.vehicleModel || '未知';
                    if (modelData[model]) {
                        modelData[model].deliveries++;
                    }
                }
            }
        });

        // 统计库存中的车型
        inventory.forEach(item => {
            const model = item.vehicleModel || '未知';
            if (!modelData[model]) {
                modelData[model] = {
                    orders: 0,
                    deliveries: 0,
                    inventory: 0,
                    revenue: 0
                };
            }
            modelData[model].inventory++;
        });

        return modelData;
    },

    // 获取目标完成情况数据
    getTargetCompletionData: function() {
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM格式
        const { targets } = this.cachedData;
        const { orders, deliveries } = this.cachedData;

        // 获取当月目标
        const currentDeliveryTarget = targets.delivery.find(t => t.yearMonth === currentMonth);
        const currentOrderTarget = targets.order.find(t => t.yearMonth === currentMonth);
        const currentRetailTarget = targets.retail.find(t => t.yearMonth === currentMonth);

        // 计算当月实际完成情况
        const currentMonthOrders = orders.filter(order => {
            const orderDate = new Date(order.orderDate);
            return orderDate.toISOString().slice(0, 7) === currentMonth;
        }).length;

        const currentMonthDeliveries = deliveries.filter(delivery => {
            if (!delivery.deliveryDate) return false;
            const deliveryDate = new Date(delivery.deliveryDate);
            return deliveryDate.toISOString().slice(0, 7) === currentMonth;
        }).length;

        const currentMonthRevenue = deliveries.filter(delivery => {
            if (!delivery.deliveryDate) return false;
            const deliveryDate = new Date(delivery.deliveryDate);
            return deliveryDate.toISOString().slice(0, 7) === currentMonth;
        }).reduce((sum, delivery) => {
            const order = orders.find(o => o.serialNumber === delivery.serialNumber);
            return sum + (order ? parseFloat(order.totalPrice || 0) : 0);
        }, 0);

        return {
            delivery: {
                target: currentDeliveryTarget ? currentDeliveryTarget.target : 0,
                actual: currentMonthDeliveries,
                completion: currentDeliveryTarget ?
                    Math.floor((currentMonthDeliveries / currentDeliveryTarget.target) * 100) : 0
            },
            order: {
                target: currentOrderTarget ? currentOrderTarget.target : 0,
                actual: currentMonthOrders,
                completion: currentOrderTarget ?
                    Math.floor((currentMonthOrders / currentOrderTarget.target) * 100) : 0
            },
            retail: {
                target: currentRetailTarget ? currentRetailTarget.target : 0,
                actual: currentMonthRevenue,
                completion: currentRetailTarget ?
                    Math.floor((currentMonthRevenue / currentRetailTarget.target) * 100) : 0
            }
        };
    },

    // 渲染分析页面
    renderAnalytics: function() {
        // HTML内容已经在页面中定义，直接渲染图表和表格
        this.renderAllCharts();
        this.renderAllTables();
    },

    // 渲染所有图表
    renderAllCharts: function() {
        this.renderAdvisorRankingChart();
        this.renderSalesFunnelOverview();
        this.renderLeadFunnelOverview();
        this.renderLeadChannelChart();
        this.renderSalesTrendChart();
        this.renderLeadTrendChart();
    },

    // 渲染销售漏斗概览
    renderSalesFunnelOverview: function() {
        const container = document.getElementById('sales-funnel-container');
        if (!container) return;

        const funnelData = this.getSalesFunnelData();

        container.innerHTML = `
            <div class="funnel-container">
                <div class="funnel-stage stage-1">
                    <div>总客户: ${funnelData.leads}</div>
                    <div style="font-size: 10px; opacity: 0.8;">转化率: 100%</div>
                </div>
                <div class="funnel-stage stage-2">
                    <div>有效线索: ${Math.floor(funnelData.leads * 0.67)}</div>
                    <div style="font-size: 10px; opacity: 0.8;">转化率: 67%</div>
                </div>
                <div class="funnel-stage stage-3">
                    <div>到店: ${funnelData.showroomVisits}</div>
                    <div style="font-size: 10px; opacity: 0.8;">转化率: ${funnelData.conversion.leadsToShowroom}%</div>
                </div>
                <div class="funnel-stage stage-4">
                    <div>订单: ${funnelData.orders}</div>
                    <div style="font-size: 10px; opacity: 0.8;">转化率: ${funnelData.conversion.testDriveToOrder}%</div>
                </div>
                <div class="funnel-stage stage-5">
                    <div>订单: ${funnelData.deliveries}</div>
                    <div style="font-size: 10px; opacity: 0.8;">转化率: ${funnelData.conversion.orderToDelivery}%</div>
                </div>
            </div>
            <div class="funnel-stats">
                <div style="margin-top: 10px; font-size: 10px; color: #666;">
                    整体转化率: ${((funnelData.deliveries / funnelData.leads) * 100).toFixed(1)}%
                </div>
            </div>
        `;
    },

    // 渲染线索漏斗概览
    renderLeadFunnelOverview: function() {
        const container = document.getElementById('lead-funnel-container');
        if (!container) return;

        const { leadEntries } = this.cachedData;
        const totalLeads = leadEntries.length;
        const validLeads = leadEntries.filter(lead => lead.isValid).length;
        const contactedLeads = leadEntries.filter(lead => lead.contactStatus === '已联系').length;
        const qualifiedLeads = leadEntries.filter(lead => lead.intention && ['A', 'B'].includes(lead.intention)).length;
        const convertedLeads = leadEntries.filter(lead => lead.status === '已成交').length;

        container.innerHTML = `
            <div class="funnel-container">
                <div class="funnel-stage stage-1">
                    <div>总线索: ${totalLeads}</div>
                    <div style="font-size: 10px; opacity: 0.8;">转化率: 100%</div>
                </div>
                <div class="funnel-stage stage-2">
                    <div>有效线索: ${validLeads}</div>
                    <div style="font-size: 10px; opacity: 0.8;">转化率: ${totalLeads > 0 ? ((validLeads / totalLeads) * 100).toFixed(1) : 0}%</div>
                </div>
                <div class="funnel-stage stage-3">
                    <div>已联系: ${contactedLeads}</div>
                    <div style="font-size: 10px; opacity: 0.8;">转化率: ${validLeads > 0 ? ((contactedLeads / validLeads) * 100).toFixed(1) : 0}%</div>
                </div>
                <div class="funnel-stage stage-4">
                    <div>到店: ${qualifiedLeads}</div>
                    <div style="font-size: 10px; opacity: 0.8;">转化率: ${contactedLeads > 0 ? ((qualifiedLeads / contactedLeads) * 100).toFixed(1) : 0}%</div>
                </div>
                <div class="funnel-stage stage-5">
                    <div>订单: ${convertedLeads}</div>
                    <div style="font-size: 10px; opacity: 0.8;">转化率: ${qualifiedLeads > 0 ? ((convertedLeads / qualifiedLeads) * 100).toFixed(1) : 0}%</div>
                </div>
            </div>
            <div class="funnel-stats">
                <div style="margin-top: 10px; font-size: 10px; color: #666;">
                    整体转化率: ${totalLeads > 0 ? ((convertedLeads / totalLeads) * 100).toFixed(1) : 0}%
                </div>
            </div>
        `;
    },

    // 渲染线索渠道图表
    renderLeadChannelChart: function() {
        const ctx = document.getElementById('lead-channel-chart');
        if (!ctx) return;

        const { leadEntries } = this.cachedData;
        const channelData = {};

        // 统计各渠道线索数量
        leadEntries.forEach(lead => {
            const channel = lead.channel || '未知';
            channelData[channel] = (channelData[channel] || 0) + 1;
        });

        const channels = Object.keys(channelData);
        const counts = Object.values(channelData);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: channels,
                datasets: [{
                    label: '线索数量',
                    data: counts,
                    backgroundColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0',
                        '#06ffa5',
                        '#f77f00',
                        '#fcbf49'
                    ],
                    borderColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0',
                        '#06ffa5',
                        '#f77f00',
                        '#fcbf49'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: '#e9ecef'
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            },
                            maxRotation: 45
                        }
                    }
                }
            }
        });
    },

    // 渲染销售趋势图表
    renderSalesTrendChart: function() {
        const ctx = document.getElementById('sales-trend-chart');
        if (!ctx) return;

        const trendData = this.getMonthlySalesTrend(6); // 最近6个月

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.map(item => item.monthName.replace('年', '/').replace('月', '')),
                datasets: [
                    {
                        label: '新订单客户',
                        data: trendData.map(item => item.orders),
                        borderColor: '#4361ee',
                        backgroundColor: 'rgba(67, 97, 238, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 4,
                        pointBackgroundColor: '#4361ee'
                    },
                    {
                        label: '成交客户',
                        data: trendData.map(item => item.deliveries),
                        borderColor: '#4cc9f0',
                        backgroundColor: 'rgba(76, 201, 240, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 4,
                        pointBackgroundColor: '#4cc9f0'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                size: 10
                            },
                            usePointStyle: true
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: '#e9ecef'
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    },

    // 渲染线索趋势图表
    renderLeadTrendChart: function() {
        const ctx = document.getElementById('lead-trend-chart');
        if (!ctx) return;

        const leadTrendData = this.getLeadTrendData(6); // 最近6个月

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: leadTrendData.map(item => item.monthName.replace('年', '/').replace('月', '')),
                datasets: [
                    {
                        label: '新线索客户',
                        data: leadTrendData.map(item => item.newLeads),
                        borderColor: '#4361ee',
                        backgroundColor: 'rgba(67, 97, 238, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 4,
                        pointBackgroundColor: '#4361ee'
                    },
                    {
                        label: '成交客户',
                        data: leadTrendData.map(item => item.convertedLeads),
                        borderColor: '#4cc9f0',
                        backgroundColor: 'rgba(76, 201, 240, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 4,
                        pointBackgroundColor: '#4cc9f0'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                size: 10
                            },
                            usePointStyle: true
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: '#e9ecef'
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    },

    // 获取线索趋势数据
    getLeadTrendData: function(months = 6) {
        const { leadEntries } = this.cachedData;
        const now = new Date();
        const monthlyData = [];

        for (let i = months - 1; i >= 0; i--) {
            const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

            const monthLeads = leadEntries.filter(lead => {
                const leadDate = new Date(lead.createDate || lead.visitDate);
                return leadDate.getFullYear() === date.getFullYear() &&
                       leadDate.getMonth() === date.getMonth();
            });

            const convertedLeads = monthLeads.filter(lead => lead.status === '已成交');

            monthlyData.push({
                month: monthKey,
                monthName: `${date.getFullYear()}年${date.getMonth() + 1}月`,
                newLeads: monthLeads.length,
                convertedLeads: convertedLeads.length
            });
        }

        return monthlyData;
    },

    // 渲染销售顾问排名图表
    renderAdvisorRankingChart: function() {
        const ctx = document.getElementById('advisor-ranking-chart');
        if (!ctx) return;

        // 获取销售顾问业绩数据
        const performanceData = this.getSalesAdvisorPerformance();
        const advisors = Object.keys(performanceData)
            .sort((a, b) => performanceData[b].orders - performanceData[a].orders)
            .slice(0, 6); // 取前6名

        const orderCounts = advisors.map(advisor => performanceData[advisor].orders);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: advisors,
                datasets: [{
                    label: '订单数量',
                    data: orderCounts,
                    backgroundColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0',
                        '#06ffa5'
                    ],
                    borderColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0',
                        '#06ffa5'
                    ],
                    borderWidth: 1,
                    barThickness: 20
                }]
            },
            options: {
                indexAxis: 'y', // 横向条形图
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: '#e9ecef'
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    },

    // 渲染所有表格
    renderAllTables: function() {
        this.renderShowroomTrafficTable();
        this.renderAdvisorPerformanceTable();
    },

    // 渲染展厅客流表格
    renderShowroomTrafficTable: function() {
        const container = document.getElementById('showroom-traffic-table');
        if (!container) return;

        // 计算展厅客流数据
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();

        const monthlyData = this.calculateMonthlyTraffic(currentYear, currentMonth);

        container.innerHTML = `
            <table style="width: 100%; border-collapse: collapse; font-size: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style="background: #f1f3f4; color: #333;">
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: left; font-weight: 600;">到店日期</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">今日</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">本月</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">全年</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">月占比</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="background: white;">
                        <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333;">首次</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.todayFirst}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.monthFirst}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.yearFirst}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.firstPercentage}%</td>
                    </tr>
                    <tr style="background: #fafafa;">
                        <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333;">再次到店</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.todayReturn}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.monthReturn}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.yearReturn}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.returnPercentage}%</td>
                    </tr>
                    <tr style="background: white;">
                        <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333;">再次</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">1</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">5</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">34</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">11.1%</td>
                    </tr>
                    <tr style="background: #fafafa;">
                        <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333;">再次到店</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">2</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">8</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">26</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">5.2%</td>
                    </tr>
                </tbody>
            </table>
            <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                <div style="display: flex; justify-content: space-between; font-size: 10px; color: #666;">
                    <span>主要客流</span>
                    <span>科室</span>
                    <span>配车类型</span>
                    <span>L车型</span>
                    <span>现有库存</span>
                </div>
                <div style="display: flex; justify-content: space-between; font-size: 10px; color: #333; margin-top: 4px;">
                    <span>月计划</span>
                    <span>科室</span>
                    <span>配车类型</span>
                    <span>L车型</span>
                    <span>现有库存</span>
                </div>
                <div style="display: flex; justify-content: space-between; font-size: 10px; color: #333; margin-top: 4px;">
                    <span>全年</span>
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
    },

    // 计算月度客流数据
    calculateMonthlyTraffic: function(year, month) {
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];

        // 使用展厅录入数据
        const { showroomEntries } = this.cachedData;

        // 筛选当月客户
        const monthCustomers = showroomEntries.filter(entry => {
            const visitDate = new Date(entry.visitDate);
            return visitDate.getFullYear() === year && visitDate.getMonth() === month;
        });

        // 筛选当年客户
        const yearCustomers = showroomEntries.filter(entry => {
            const visitDate = new Date(entry.visitDate);
            return visitDate.getFullYear() === year;
        });

        // 筛选今日客户
        const todayCustomers = showroomEntries.filter(entry => {
            return entry.visitDate === todayStr;
        });

        // 计算首次和再次到店
        const todayFirst = todayCustomers.filter(c => c.visitType === '首次到店').length;
        const todayReturn = todayCustomers.filter(c => c.visitType === '再次到店').length;

        const monthFirst = monthCustomers.filter(c => c.visitType === '首次到店').length;
        const monthReturn = monthCustomers.filter(c => c.visitType === '再次到店').length;

        const yearFirst = yearCustomers.filter(c => c.visitType === '首次到店').length;
        const yearReturn = yearCustomers.filter(c => c.visitType === '再次到店').length;

        const monthTotal = monthFirst + monthReturn;
        const firstPercentage = monthTotal > 0 ? Math.round((monthFirst / monthTotal) * 100) : 0;
        const returnPercentage = monthTotal > 0 ? Math.round((monthReturn / monthTotal) * 100) : 0;

        return {
            todayFirst,
            todayReturn,
            todayTotal: todayFirst + todayReturn,
            monthFirst,
            monthReturn,
            monthTotal,
            yearFirst,
            yearReturn,
            yearTotal: yearFirst + yearReturn,
            firstPercentage,
            returnPercentage
        };
    },

    // 渲染销售顾问成果分析表格
    renderAdvisorPerformanceTable: function() {
        const container = document.getElementById('advisor-performance-table');
        if (!container) return;

        // 计算销售顾问成果数据
        const advisorStats = this.calculateAdvisorPerformanceStats();

        container.innerHTML = `
            <table style="width: 100%; border-collapse: collapse; font-size: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style="background: #f1f3f4; color: #333;">
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: left; font-weight: 600;">销售顾问</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">A</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">B</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">C</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">F</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">合计</th>
                    </tr>
                </thead>
                <tbody>
                    ${advisorStats.map((advisor, index) => `
                        <tr style="background: ${index % 2 === 0 ? 'white' : '#fafafa'};">
                            <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333;">${advisor.name}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${advisor.gradeA}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${advisor.gradeB}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${advisor.gradeC}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${advisor.gradeF}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${advisor.total}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 8px; font-size: 10px;">
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">销售顾问</div>
                        <div style="color: #333; font-weight: 600;">首次</div>
                        <div style="color: #333;">再次</div>
                        <div style="color: #333;">王五</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">首次到店</div>
                        <div style="color: #333; font-weight: 600;">H</div>
                        <div style="color: #333;">科室</div>
                        <div style="color: #333;">配车</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">接待时间</div>
                        <div style="color: #333; font-weight: 600;">A</div>
                        <div style="color: #333;">配车类型</div>
                        <div style="color: #333;">意向车</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">意向车</div>
                        <div style="color: #333; font-weight: 600;">B</div>
                        <div style="color: #333;">试驾车</div>
                        <div style="color: #333;">成交率</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">试驾车</div>
                        <div style="color: #333; font-weight: 600;">C</div>
                        <div style="color: #333;">试驾率</div>
                        <div style="color: #333;">成交率</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">成交率</div>
                        <div style="color: #333; font-weight: 600;">合计</div>
                        <div style="color: #333;">成交率</div>
                        <div style="color: #333;">成交率</div>
                    </div>
                </div>
            </div>
        `;
    },

    // 计算销售顾问成果统计数据
    calculateAdvisorPerformanceStats: function() {
        const { showroomEntries } = this.cachedData;
        const advisorMap = new Map();

        // 统计各销售顾问的客户意向等级
        showroomEntries.forEach(entry => {
            const advisor = entry.salesAdvisor || '未知';
            if (!advisorMap.has(advisor)) {
                advisorMap.set(advisor, { name: advisor, gradeA: 0, gradeB: 0, gradeC: 0, gradeF: 0, total: 0 });
            }

            const stats = advisorMap.get(advisor);
            const intention = entry.intention || 'F';

            switch (intention) {
                case 'A': stats.gradeA++; break;
                case 'B': stats.gradeB++; break;
                case 'C': stats.gradeC++; break;
                default: stats.gradeF++; break;
            }
            stats.total++;
        });

        return Array.from(advisorMap.values())
            .sort((a, b) => b.total - a.total)
            .slice(0, 5); // 取前5名
    },

    // 原有的渲染函数保持不变，但需要修改容器ID
    renderOldAnalytics: function() {
        const container = document.getElementById('analytics-content-old');
        if (!container) return;

        container.innerHTML = `
            <div class="analytics-dashboard">
                <!-- 时间筛选器 -->
                <div class="time-filter-section">
                    <div class="filter-controls">
                        <label>时间范围：</label>
                        <select id="time-range-select">
                            <option value="3">最近3个月</option>
                            <option value="6">最近6个月</option>
                            <option value="12" selected>最近12个月</option>
                        </select>
                        <button class="btn btn-primary" id="refresh-data-btn">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                        <button class="btn btn-success" id="export-data-btn">
                            <i class="fas fa-download"></i> 导出报表
                        </button>
                    </div>
                </div>

                <!-- 销售漏斗分析 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-filter"></i> 销售漏斗分析</h3>
                        <p>展示从线索到交付的完整转化流程</p>
                    </div>
                    <div class="chart-container">
                        <canvas id="sales-funnel-chart"></canvas>
                    </div>
                    <div class="funnel-stats" id="funnel-stats"></div>
                </div>

                <!-- 月度销售趋势 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-chart-line"></i> 月度销售趋势</h3>
                        <p>订单数量、交付数量和销售额变化趋势</p>
                    </div>
                    <div class="chart-container">
                        <canvas id="monthly-trend-chart"></canvas>
                    </div>
                </div>

                <!-- 客户来源分析 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-users"></i> 客户来源分析</h3>
                        <p>不同渠道的线索质量和转化效果</p>
                    </div>
                    <div class="chart-container">
                        <canvas id="customer-source-chart"></canvas>
                    </div>
                </div>

                <!-- 销售顾问业绩排行 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-trophy"></i> 销售顾问业绩排行</h3>
                        <p>个人订单数、交付数、销售额等指标</p>
                    </div>
                    <div class="chart-container">
                        <canvas id="advisor-performance-chart"></canvas>
                    </div>
                </div>

                <!-- 车型销售分析 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-car"></i> 车型销售分析</h3>
                        <p>热销车型、库存周转率、配车效率</p>
                    </div>
                    <div class="chart-container">
                        <canvas id="vehicle-model-chart"></canvas>
                    </div>
                </div>

                <!-- 目标完成情况 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-bullseye"></i> 目标完成情况</h3>
                        <p>提车目标、订单目标、零售目标的达成率对比</p>
                    </div>
                    <div class="target-dashboard" id="target-dashboard"></div>
                </div>
            </div>
        `;

        // 渲染所有图表
        this.renderAllCharts();
    },

    // 渲染所有图表
    renderAllCharts: function() {
        this.renderSalesFunnelChart();
        this.renderMonthlyTrendChart();
        this.renderCustomerSourceChart();
        this.renderAdvisorPerformanceChart();
        this.renderVehicleModelChart();
        this.renderTargetDashboard();
    },

    // 渲染销售漏斗图表
    renderSalesFunnelChart: function() {
        const funnelData = this.getSalesFunnelData();
        const ctx = document.getElementById('sales-funnel-chart');
        if (!ctx) return;

        // 销售漏斗数据
        const stages = ['线索', '展厅来访', '试驾', '订单', '交付'];
        const values = [
            funnelData.leads,
            funnelData.showroomVisits,
            funnelData.testDrives,
            funnelData.orders,
            funnelData.deliveries
        ];

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: stages,
                datasets: [{
                    label: '数量',
                    data: values,
                    backgroundColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0'
                    ],
                    borderColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '销售漏斗转化分析'
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量'
                        }
                    }
                }
            }
        });

        // 渲染转化率统计
        this.renderFunnelStats(funnelData);
    },

    // 渲染漏斗统计信息
    renderFunnelStats: function(funnelData) {
        const statsContainer = document.getElementById('funnel-stats');
        if (!statsContainer) return;

        statsContainer.innerHTML = `
            <div class="funnel-stats-grid">
                <div class="stat-item">
                    <div class="stat-label">线索→展厅转化率</div>
                    <div class="stat-value">${funnelData.conversion.leadsToShowroom}%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">展厅→试驾转化率</div>
                    <div class="stat-value">${funnelData.conversion.showroomToTestDrive}%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">试驾→订单转化率</div>
                    <div class="stat-value">${funnelData.conversion.testDriveToOrder}%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">订单→交付转化率</div>
                    <div class="stat-value">${funnelData.conversion.orderToDelivery}%</div>
                </div>
            </div>
        `;
    },

    // 绑定事件
    bindEvents: function() {
        // 时间范围选择事件
        const timeRangeSelect = document.getElementById('time-range-select');
        if (timeRangeSelect) {
            timeRangeSelect.addEventListener('change', () => {
                this.renderAllCharts();
            });
        }

        // 刷新数据事件
        const refreshBtn = document.getElementById('refresh-data-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                await this.loadAllData();
                this.renderAllCharts();
                alert('数据已刷新');
            });
        }

        // 导出数据事件
        const exportBtn = document.getElementById('export-data-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportAnalyticsData();
            });
        }
    },

    // 渲染月度销售趋势图表
    renderMonthlyTrendChart: function() {
        const timeRange = document.getElementById('time-range-select')?.value || 12;
        const trendData = this.getMonthlySalesTrend(parseInt(timeRange));
        const ctx = document.getElementById('monthly-trend-chart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.map(item => item.monthName),
                datasets: [
                    {
                        label: '订单数量',
                        data: trendData.map(item => item.orders),
                        borderColor: '#4361ee',
                        backgroundColor: 'rgba(67, 97, 238, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: '交付数量',
                        data: trendData.map(item => item.deliveries),
                        borderColor: '#f72585',
                        backgroundColor: 'rgba(247, 37, 133, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '月度销售趋势'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量'
                        }
                    }
                }
            }
        });
    },

    // 渲染客户来源分析图表
    renderCustomerSourceChart: function() {
        const sourceData = this.getCustomerSourceAnalysis();
        const ctx = document.getElementById('customer-source-chart');
        if (!ctx) return;

        const sources = Object.keys(sourceData);
        const leadsData = sources.map(source => sourceData[source].leads);
        const showroomData = sources.map(source => sourceData[source].showroom);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: sources,
                datasets: [
                    {
                        label: '线索数量',
                        data: leadsData,
                        backgroundColor: '#4361ee'
                    },
                    {
                        label: '展厅来访',
                        data: showroomData,
                        backgroundColor: '#f72585'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '客户来源分析'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量'
                        }
                    }
                }
            }
        });
    },

    // 渲染销售顾问业绩图表
    renderAdvisorPerformanceChart: function() {
        const performanceData = this.getSalesAdvisorPerformance();
        const ctx = document.getElementById('advisor-performance-chart');
        if (!ctx) return;

        const advisors = Object.keys(performanceData).slice(0, 10); // 只显示前10名
        const ordersData = advisors.map(advisor => performanceData[advisor].orders);
        const deliveriesData = advisors.map(advisor => performanceData[advisor].deliveries);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: advisors,
                datasets: [
                    {
                        label: '订单数量',
                        data: ordersData,
                        backgroundColor: '#4361ee'
                    },
                    {
                        label: '交付数量',
                        data: deliveriesData,
                        backgroundColor: '#f72585'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '销售顾问业绩排行（前10名）'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量'
                        }
                    }
                }
            }
        });
    },

    // 渲染车型销售分析图表
    renderVehicleModelChart: function() {
        const modelData = this.getVehicleModelAnalysis();
        const ctx = document.getElementById('vehicle-model-chart');
        if (!ctx) return;

        const models = Object.keys(modelData).slice(0, 8); // 只显示前8个车型
        const ordersData = models.map(model => modelData[model].orders);
        const deliveriesData = models.map(model => modelData[model].deliveries);

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: models,
                datasets: [{
                    label: '订单数量',
                    data: ordersData,
                    backgroundColor: [
                        '#4361ee', '#f72585', '#4cc9f0', '#7209b7',
                        '#3f37c9', '#f77f00', '#fcbf49', '#06ffa5'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '车型销售分布'
                    },
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    },

    // 渲染目标完成情况仪表盘
    renderTargetDashboard: function() {
        const targetData = this.getTargetCompletionData();
        const container = document.getElementById('target-dashboard');
        if (!container) return;

        container.innerHTML = `
            <div class="target-cards">
                <div class="target-card">
                    <div class="target-header">
                        <h4><i class="fas fa-truck"></i> 提车目标</h4>
                    </div>
                    <div class="target-progress">
                        <div class="progress-circle" data-percentage="${targetData.delivery.completion}">
                            <div class="progress-text">
                                <span class="percentage">${targetData.delivery.completion}%</span>
                                <span class="label">完成率</span>
                            </div>
                        </div>
                        <div class="target-stats">
                            <div class="stat">
                                <span class="label">目标:</span>
                                <span class="value">${targetData.delivery.target}</span>
                            </div>
                            <div class="stat">
                                <span class="label">实际:</span>
                                <span class="value">${targetData.delivery.actual}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="target-card">
                    <div class="target-header">
                        <h4><i class="fas fa-shopping-cart"></i> 订单目标</h4>
                    </div>
                    <div class="target-progress">
                        <div class="progress-circle" data-percentage="${targetData.order.completion}">
                            <div class="progress-text">
                                <span class="percentage">${targetData.order.completion}%</span>
                                <span class="label">完成率</span>
                            </div>
                        </div>
                        <div class="target-stats">
                            <div class="stat">
                                <span class="label">目标:</span>
                                <span class="value">${targetData.order.target}</span>
                            </div>
                            <div class="stat">
                                <span class="label">实际:</span>
                                <span class="value">${targetData.order.actual}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="target-card">
                    <div class="target-header">
                        <h4><i class="fas fa-chart-line"></i> 零售目标</h4>
                    </div>
                    <div class="target-progress">
                        <div class="progress-circle" data-percentage="${targetData.retail.completion}">
                            <div class="progress-text">
                                <span class="percentage">${targetData.retail.completion}%</span>
                                <span class="label">完成率</span>
                            </div>
                        </div>
                        <div class="target-stats">
                            <div class="stat">
                                <span class="label">目标:</span>
                                <span class="value">¥${(targetData.retail.target / 10000).toFixed(1)}万</span>
                            </div>
                            <div class="stat">
                                <span class="label">实际:</span>
                                <span class="value">¥${(targetData.retail.actual / 10000).toFixed(1)}万</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 初始化进度圆环
        this.initProgressCircles();
    },

    // 初始化进度圆环
    initProgressCircles: function() {
        const circles = document.querySelectorAll('.progress-circle');
        circles.forEach(circle => {
            const percentage = parseFloat(circle.dataset.percentage);
            const color = percentage >= 100 ? '#28a745' : percentage >= 80 ? '#ffc107' : '#dc3545';

            circle.style.background = `conic-gradient(${color} ${percentage * 3.6}deg, #e9ecef 0deg)`;
        });
    },

    // 导出分析数据
    exportAnalyticsData: function() {
        // 这个方法将在后续实现
        console.log('导出分析数据');
        alert('导出功能开发中...');
    }
};
