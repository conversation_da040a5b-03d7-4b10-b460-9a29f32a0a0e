// 测试数据脚本 - 用于验证系统功能
window.testData = {
    // 添加测试库存数据
    addTestInventory: async function() {
        const testInventory = [
            {
                serialNumber: 'INV001',
                inventoryStatus: '可售',
                carModel: '奔驰C级',
                version: 'C260L 运动版',
                vin: 'WDDGF4HB1EA123456',
                exteriorColor: '极地白',
                interiorColor: '黑色',
                factoryOptions: '全景天窗,运动套装',
                standard: '标准配置',
                location: 'A区01号',
                productionDate: '2024-01-15',
                shipmentDate: '2024-02-01',
                stockDate: '2024-02-10',
                guidePrice: '359800',
                notes: '测试车辆1'
            },
            {
                serialNumber: 'INV002',
                inventoryStatus: '可售',
                carModel: '奔驰E级',
                version: 'E300L 豪华版',
                vin: 'WDDGF4HB1EA234567',
                exteriorColor: '曜石黑',
                interiorColor: '米色',
                factoryOptions: '智能驾驶,柏林之声',
                standard: '豪华配置',
                location: 'A区02号',
                productionDate: '2024-01-20',
                shipmentDate: '2024-02-05',
                stockDate: '2024-02-15',
                guidePrice: '459800',
                notes: '测试车辆2'
            },
            {
                serialNumber: 'INV003',
                inventoryStatus: '可售',
                carModel: '奔驰GLC',
                version: 'GLC260L 4MATIC',
                vin: 'WDDGF4HB1EA345678',
                exteriorColor: '钻石银',
                interiorColor: '黑色',
                factoryOptions: '空气悬挂,AMG套装',
                standard: '豪华配置',
                location: 'B区01号',
                productionDate: '2023-12-10',
                shipmentDate: '2024-01-05',
                stockDate: '2024-01-15',
                guidePrice: '429800',
                notes: '测试车辆3 - 库龄较长'
            }
        ];

        try {
            for (const inventory of testInventory) {
                await window.dbFunctions.addInventoryManagement(inventory);
            }
            console.log('测试库存数据添加成功');
            return true;
        } catch (error) {
            console.error('添加测试库存数据失败:', error);
            return false;
        }
    },

    // 添加测试订单数据
    addTestOrders: async function() {
        const testOrders = [
            {
                serialNumber: 'ORD001',
                orderDate: '2024-07-20',
                customerName: '张三',
                phone1: '13800138001',
                auditStatus: '待审核',
                salesAdvisor: '李销售',
                vin: '',
                carModel: '奔驰C级',
                configuration: 'C260L 运动版',
                exteriorColor: '极地白',
                interiorColor: '黑色',
                options: '全景天窗,运动套装',
                deliveryStatus: '待交付',
                deliveryDate: '',
                deposit: '50000',
                contractPrice: '359800'
            },
            {
                serialNumber: 'ORD002',
                orderDate: '2024-07-18',
                customerName: '李四',
                phone1: '13800138002',
                auditStatus: '已审核',
                salesAdvisor: '王销售',
                vin: '',
                carModel: '奔驰E级',
                configuration: 'E300L 豪华版',
                exteriorColor: '曜石黑',
                interiorColor: '米色',
                options: '智能驾驶,柏林之声',
                deliveryStatus: '待交付',
                deliveryDate: '',
                deposit: '80000',
                contractPrice: '459800'
            }
        ];

        try {
            for (const order of testOrders) {
                await window.dbFunctions.addOrderManagement(order);
            }
            console.log('测试订单数据添加成功');
            return true;
        } catch (error) {
            console.error('添加测试订单数据失败:', error);
            return false;
        }
    },

    // 添加测试配车交付数据
    addTestDeliveryManagement: async function() {
        const testDelivery = [
            {
                serialNumber: 'ORD001',
                orderStatus: '待配车',
                orderDate: '2024-07-20',
                deliveryDate: '',
                customerName: '张三',
                phone1: '13800138001',
                phone2: '',
                salesAdvisor: '李销售',
                carModel: '奔驰C级',
                configuration: 'C260L 运动版',
                exteriorColor: '极地白',
                interiorColor: '黑色',
                vin: ''
            },
            {
                serialNumber: 'ORD002',
                orderStatus: '待配车',
                orderDate: '2024-07-18',
                deliveryDate: '',
                customerName: '李四',
                phone1: '13800138002',
                phone2: '',
                salesAdvisor: '王销售',
                carModel: '奔驰E级',
                configuration: 'E300L 豪华版',
                exteriorColor: '曜石黑',
                interiorColor: '米色',
                vin: ''
            }
        ];

        try {
            for (const delivery of testDelivery) {
                await window.dbFunctions.addDeliveryManagement(delivery);
            }
            console.log('测试配车交付数据添加成功');
            return true;
        } catch (error) {
            console.error('添加测试配车交付数据失败:', error);
            return false;
        }
    },

    // 添加测试目标数据
    addTestTargets: async function() {
        const currentMonth = new Date().toISOString().slice(0, 7);
        const lastMonth = new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().slice(0, 7);
        
        const testTargets = {
            delivery: [
                { yearMonth: lastMonth, target: 50, actual: 45, completionRate: 90 },
                { yearMonth: currentMonth, target: 60, actual: 30, completionRate: 50 }
            ],
            order: [
                { yearMonth: lastMonth, target: 80, actual: 85, completionRate: 106.25 },
                { yearMonth: currentMonth, target: 90, actual: 40, completionRate: 44.44 }
            ],
            retail: [
                { yearMonth: lastMonth, target: 100, actual: 95, completionRate: 95 },
                { yearMonth: currentMonth, target: 110, actual: 55, completionRate: 50 }
            ]
        };

        try {
            for (const target of testTargets.delivery) {
                await window.dbFunctions.addDeliveryTarget(target);
            }
            for (const target of testTargets.order) {
                await window.dbFunctions.addOrderTarget(target);
            }
            for (const target of testTargets.retail) {
                await window.dbFunctions.addRetailTarget(target);
            }
            console.log('测试目标数据添加成功');
            return true;
        } catch (error) {
            console.error('添加测试目标数据失败:', error);
            return false;
        }
    },

    // 初始化所有测试数据
    initAllTestData: async function() {
        console.log('开始初始化测试数据...');
        
        const results = await Promise.all([
            this.addTestInventory(),
            this.addTestOrders(),
            this.addTestDeliveryManagement(),
            this.addTestTargets()
        ]);

        const success = results.every(result => result === true);
        
        if (success) {
            console.log('所有测试数据初始化成功！');
            alert('测试数据初始化成功！请刷新页面查看效果。');
        } else {
            console.log('部分测试数据初始化失败');
            alert('部分测试数据初始化失败，请查看控制台日志。');
        }
        
        return success;
    },

    // 清理测试数据
    clearTestData: async function() {
        try {
            // 这里可以添加清理逻辑，但为了安全起见，暂时不实现
            console.log('清理测试数据功能暂未实现');
            alert('清理测试数据功能暂未实现，请手动删除测试数据。');
        } catch (error) {
            console.error('清理测试数据失败:', error);
        }
    }
};

// 在控制台中提供快捷方式
console.log('测试数据工具已加载！');
console.log('使用 testData.initAllTestData() 初始化测试数据');
console.log('使用 testData.clearTestData() 清理测试数据');
