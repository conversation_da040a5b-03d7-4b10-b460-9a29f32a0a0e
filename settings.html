<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>系统设置 - 汽车销售漏斗管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dexie@3.2.2/dist/dexie.min.js"></script>
    <link rel="stylesheet" href="styles.css" />
    <style>
        /* 试驾车型状态和类型样式 */
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-服役 {
            background-color: #d4edda;
            color: #155724;
        }

        .status-超期 {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-处置 {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-unknown {
            background-color: #e9ecef;
            color: #6c757d;
        }

        .type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-试驾车 {
            background-color: #cce5ff;
            color: #004085;
        }

        .type-代步车 {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .type-服务车 {
            background-color: #d4edda;
            color: #155724;
        }

        .type-指定车 {
            background-color: #ffeaa7;
            color: #856404;
        }

        .type-unknown {
            background-color: #e9ecef;
            color: #6c757d;
        }

        /* 表单样式优化 */
        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #212529;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4361ee;
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
        }

        /* 销售顾问状态徽章样式 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            display: inline-block;
            min-width: 50px;
        }

        .status-badge.active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-badge.inactive {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body style="height: 100vh; margin: 0;">
    <div class="container" style="flex: 1; display: flex; flex-direction: column; height: 100%;">
        <header style="background: linear-gradient(135deg, #4361ee, #3f37c9); color: white; padding: 15px 30px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
            <div class="logo" style="display: flex; align-items: center; gap: 15px;">
                <i class="fas fa-cog" style="font-size: 28px;"></i>
                <h1 style="font-size: 22px; font-weight: 600;">系统设置</h1>
            </div>
            <div class="user-info" style="display: flex; align-items: center; gap: 12px;">
                <div class="user-avatar" style="width: 42px; height: 42px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 18px;">张</div>
                <div>
                    <div>张经理</div>
                    <div style="font-size: 13px; opacity: 0.8;">销售主管</div>
                </div>
            </div>
        </header>
        
        <div class="main-content" style="flex: 1; display: flex; overflow: hidden;">
            <div class="sidebar" style="width: 260px; background: white; padding: 25px 20px; display: flex; flex-direction: column; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); z-index: 10; height: 100%; overflow-y: auto;">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 13px; color: #6c757d; margin-bottom: 5px;">系统版本</div>
                    <div style="font-size: 28px; font-weight: 700; color: #3f37c9;">v1.0</div>
                    <div style="font-size: 14px; color: #6c757d;">稳定版本</div>
                </div>
                
                <ul class="nav-menu" style="list-style: none; margin-top: 20px;">
                    <li style="margin-bottom: 8px;"><a href="index.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-home" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 首页</a></li>
                    <li style="margin-bottom: 8px;"><a href="salesanalytics.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-chart-line" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 销售分析</a></li>
                    <li style="margin-bottom: 8px;"><a href="customers.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 客户管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="order.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-shopping-cart" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 订单管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="parts.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-boxes" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 配件库管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="settings.html" class="active" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #4361ee; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #4361ee;"></i> 系统设置</a></li>
                    <li style="margin-bottom: 8px;"><a href="user.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 用户管理</a></li>
                </ul>
                
                <div style="margin-top: 30px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h3 style="font-size: 15px; margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-tools" style="color: #3f37c9;"></i> 快速操作
                    </h3>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <button class="btn btn-outline" onclick="importData()">
                            <i class="fas fa-upload"></i> 导入数据
                        </button>
                        <button class="btn btn-outline" onclick="backupData()">
                            <i class="fas fa-download"></i> 备份数据
                        </button>
                        <button class="btn btn-outline" onclick="clearCache()">
                            <i class="fas fa-trash"></i> 清理缓存
                        </button>
                        <button class="btn btn-outline" onclick="resetSettings()">
                            <i class="fas fa-undo"></i> 重置设置
                        </button>
                    </div>

                    <!-- 隐藏的文件输入框 -->
                    <input type="file" id="import-file-input" accept=".json" style="display: none;" onchange="handleFileImport(event)">
                </div>
            </div>
            
            <div class="content-area" style="flex: 1; padding: 30px; overflow-y: auto;">
                <div class="module-container active" id="settings-module">
                    <div class="module-header">
                        <h1><i class="fas fa-cog"></i> 系统设置</h1>
                        <div style="font-size: 14px; color: #6c757d;">配置系统参数和用户设置</div>
                    </div>
                    
                    <div class="card">
                        <div class="settings-tabs">
                            <div class="settings-tab active" data-tab="sales-advisors">销售顾问</div>
                            <div class="settings-tab" data-tab="car-models">车型管理</div>
                            <div class="settings-tab" data-tab="intentions">意向管理</div>
                            <div class="settings-tab" data-tab="competitors">竞品管理</div>
                            <div class="settings-tab" data-tab="regions">区域管理</div>
                            <div class="settings-tab" data-tab="channels">渠道管理</div>
                            <div class="settings-tab" data-tab="visit-types">来店类型</div>
                            <div class="settings-tab" data-tab="test-drive-models">试驾车型</div>
                        </div>
                        
                        <div class="settings-content" id="settings-content">
                            <!-- 内容由settingsModule.js动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript文件引用 -->
    <script src="database.js"></script>
    <script src="settingsModule.js"></script>
    <script src="notification.js"></script>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 等待所有脚本加载完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 检查必要的函数是否存在
                if (!window.dbFunctions) {
                    throw new Error('数据库函数未加载');
                }

                if (!window.loadSettings) {
                    throw new Error('设置模块未加载');
                }

                // 初始化数据库
                await window.dbFunctions.initDB();
                await window.dbFunctions.seedDemoData();

                // 加载设置模块
                await window.loadSettings();

                console.log('系统设置页面初始化完成');
            } catch (error) {
                console.error('初始化失败:', error);
                if (window.showNotification) {
                    showNotification('初始化失败', '系统初始化时出错: ' + error.message, 'danger');
                } else {
                    alert('初始化失败: ' + error.message);
                }
            }
        });

        // 快速操作函数
        async function backupData() {
            try {
                // 检查数据库函数是否可用
                if (!window.dbFunctions) {
                    throw new Error('数据库函数未初始化');
                }

                // 导出所有数据
                const data = {
                    timestamp: new Date().toISOString(),
                    version: '2.0'
                };

                // 安全地获取各表数据，添加空值检查
                try {
                    data.inputPersonnel = await window.dbFunctions.getAllInputPersonnel() || [];
                } catch (e) {
                    console.warn('获取录入人员数据失败:', e);
                    data.inputPersonnel = [];
                }

                try {
                    data.salesAdvisors = await window.dbFunctions.getAllSalesAdvisors() || [];
                } catch (e) {
                    console.warn('获取销售顾问数据失败:', e);
                    data.salesAdvisors = [];
                }

                try {
                    data.carModels = await window.dbFunctions.getAllCarModels() || [];
                } catch (e) {
                    console.warn('获取车型数据失败:', e);
                    data.carModels = [];
                }

                try {
                    data.intentions = await window.dbFunctions.getAllIntentions() || [];
                } catch (e) {
                    console.warn('获取意向数据失败:', e);
                    data.intentions = [];
                }

                try {
                    data.competitors = await window.dbFunctions.getAllCompetitors() || [];
                } catch (e) {
                    console.warn('获取竞品数据失败:', e);
                    data.competitors = [];
                }

                try {
                    data.regions = await window.dbFunctions.getAllRegions() || [];
                } catch (e) {
                    console.warn('获取区域数据失败:', e);
                    data.regions = [];
                }

                try {
                    data.channels = await window.dbFunctions.getAllChannels() || [];
                } catch (e) {
                    console.warn('获取渠道数据失败:', e);
                    data.channels = [];
                }

                try {
                    data.leadChannels = await window.dbFunctions.getAllLeadChannels() || [];
                } catch (e) {
                    console.warn('获取线索渠道数据失败:', e);
                    data.leadChannels = [];
                }

                try {
                    data.visitTypes = await window.dbFunctions.getAllVisitTypes() || [];
                } catch (e) {
                    console.warn('获取来店类型数据失败:', e);
                    data.visitTypes = [];
                }

                try {
                    data.testDriveModels = await window.dbFunctions.getAllTestDriveModels() || [];
                } catch (e) {
                    console.warn('获取试驾车型数据失败:', e);
                    data.testDriveModels = [];
                }

                try {
                    data.definitions = await window.dbFunctions.getAllDefinitions() || [];
                } catch (e) {
                    console.warn('获取定义数据失败:', e);
                    data.definitions = [];
                }

                try {
                    data.customerManagement = await window.dbFunctions.getAllCustomerManagement() || [];
                } catch (e) {
                    console.warn('获取客户管理数据失败:', e);
                    data.customerManagement = [];
                }

                try {
                    data.showroomEntries = await window.dbFunctions.getAllShowroomEntries() || [];
                } catch (e) {
                    console.warn('获取展厅录入数据失败:', e);
                    data.showroomEntries = [];
                }

                try {
                    data.leadEntries = await window.dbFunctions.getAllLeadEntries() || [];
                } catch (e) {
                    console.warn('获取线索录入数据失败:', e);
                    data.leadEntries = [];
                }

                try {
                    data.orderManagement = await window.dbFunctions.getAllOrderManagement() || [];
                } catch (e) {
                    console.warn('获取订单管理数据失败:', e);
                    data.orderManagement = [];
                }

                try {
                    data.deliveryManagement = await window.dbFunctions.getAllDeliveryManagement() || [];
                } catch (e) {
                    console.warn('获取配车交付数据失败:', e);
                    data.deliveryManagement = [];
                }

                try {
                    data.inventoryManagement = await window.dbFunctions.getAllInventoryManagement() || [];
                } catch (e) {
                    console.warn('获取库存管理数据失败:', e);
                    data.inventoryManagement = [];
                }

                try {
                    data.partsSettings = await window.dbFunctions.getAllPartsSettings() || [];
                } catch (e) {
                    console.warn('获取配件设置数据失败:', e);
                    data.partsSettings = [];
                }

                try {
                    data.partsInbound = await window.dbFunctions.getAllPartsInbound() || [];
                } catch (e) {
                    console.warn('获取入库管理数据失败:', e);
                    data.partsInbound = [];
                }

                try {
                    data.partsOutbound = await window.dbFunctions.getAllPartsOutbound() || [];
                } catch (e) {
                    console.warn('获取出库管理数据失败:', e);
                    data.partsOutbound = [];
                }

                try {
                    data.partsLending = await window.dbFunctions.getAllPartsLending() || [];
                } catch (e) {
                    console.warn('获取借出归还数据失败:', e);
                    data.partsLending = [];
                }

                const dataStr = JSON.stringify(data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                showNotification('备份完成', '数据已成功导出到本地', 'success');
            } catch (error) {
                console.error('备份失败:', error);
                showNotification('备份失败', '数据备份时出错: ' + error.message, 'danger');
            }
        }

        function clearCache() {
            try {
                // 清理浏览器缓存
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }

                // 清理localStorage
                localStorage.clear();

                showNotification('清理完成', '缓存已清理', 'success');
            } catch (error) {
                console.error('清理缓存失败:', error);
                showNotification('清理失败', '清理缓存时出错', 'danger');
            }
        }

        function resetSettings() {
            if (confirm('确定要重置所有设置吗？这将清除所有数据！')) {
                try {
                    // 清理数据库
                    indexedDB.deleteDatabase('AutoSalesDB');

                    // 清理localStorage
                    localStorage.clear();

                    // 重新加载页面
                    setTimeout(() => {
                        location.reload();
                    }, 1000);

                    showNotification('重置完成', '设置已重置，页面将重新加载', 'success');
                } catch (error) {
                    console.error('重置失败:', error);
                    showNotification('重置失败', '重置设置时出错', 'danger');
                }
            }
        }

        // 添加强制重置函数
        function forceReset() {
            // 强制删除数据库
            indexedDB.deleteDatabase('AutoSalesDB');
            localStorage.clear();
            location.reload();
        }

        // 在控制台中提供强制重置功能
        window.forceReset = forceReset;

        // 导入数据功能
        function importData() {
            // 触发隐藏的文件输入框点击事件
            document.getElementById('import-file-input').click();
        }

        // 处理文件导入
        async function handleFileImport(event) {
            const file = event.target.files[0];
            if (!file) return;

            // 创建进度提示
            const progressOverlay = document.createElement('div');
            progressOverlay.className = 'import-progress-overlay';

            const progressContainer = document.createElement('div');
            progressContainer.className = 'import-progress-container';

            const progressTitle = document.createElement('h3');
            progressTitle.className = 'import-progress-title';
            progressTitle.textContent = '正在导入数据...';

            const progressBar = document.createElement('div');
            progressBar.className = 'import-progress-bar';

            const progressFill = document.createElement('div');
            progressFill.className = 'import-progress-fill';

            const progressText = document.createElement('div');
            progressText.className = 'import-progress-text';
            progressText.textContent = '准备导入...';

            progressBar.appendChild(progressFill);
            progressContainer.appendChild(progressTitle);
            progressContainer.appendChild(progressBar);
            progressContainer.appendChild(progressText);
            progressOverlay.appendChild(progressContainer);
            document.body.appendChild(progressOverlay);

            try {
                // 读取文件内容
                const reader = new FileReader();

                reader.onload = async function(e) {
                    try {
                        // 解析JSON数据
                        const data = JSON.parse(e.target.result);

                        // 验证数据格式
                        if (!validateImportData(data)) {
                            throw new Error('导入的数据格式不正确');
                        }

                        // 更新进度
                        progressFill.style.width = '20%';
                        progressText.textContent = '验证数据完成，开始导入...';

                        // 导入数据到数据库
                        await importDataToDatabase(data, progressFill, progressText);

                        // 完成导入
                        progressFill.style.width = '100%';
                        progressTitle.textContent = '导入成功';
                        progressText.textContent = '数据已成功导入';
                        progressTitle.style.color = '#28a745';

                        // 3秒后关闭进度提示
                        setTimeout(() => {
                            document.body.removeChild(progressOverlay);
                            // 刷新当前模块
                            refreshCurrentModule();
                        }, 3000);

                    } catch (error) {
                        console.error('导入数据失败:', error);
                        progressTitle.textContent = '导入失败';
                        progressTitle.style.color = '#dc3545';
                        progressText.textContent = error.message || '导入数据时出错';

                        // 5秒后关闭进度提示
                        setTimeout(() => {
                            document.body.removeChild(progressOverlay);
                        }, 5000);
                    }
                };

                reader.onerror = function() {
                    progressTitle.textContent = '导入失败';
                    progressTitle.style.color = '#dc3545';
                    progressText.textContent = '读取文件时出错';

                    // 5秒后关闭进度提示
                    setTimeout(() => {
                        document.body.removeChild(progressOverlay);
                    }, 5000);
                };

                // 开始读取文件
                reader.readAsText(file);

            } catch (error) {
                console.error('导入数据失败:', error);
                document.body.removeChild(progressOverlay);
                showNotification('导入失败', error.message || '导入数据时出错', 'danger');
            }

            // 清空文件输入框，以便再次选择同一文件
            event.target.value = '';
        }

        // 验证导入的数据格式
        function validateImportData(data) {
            try {
                // 检查数据是否为对象
                if (!data || typeof data !== 'object') {
                    console.error('数据验证失败: 数据不是有效的对象');
                    return false;
                }

                // 检查基本属性
                if (!data.timestamp || !data.version) {
                    console.error('数据验证失败: 缺少时间戳或版本信息');
                    return false;
                }

                // 检查是否至少有一个数据表
                const dataKeys = Object.keys(data).filter(key =>
                    key !== 'timestamp' && key !== 'version' && Array.isArray(data[key])
                );

                if (dataKeys.length === 0) {
                    console.error('数据验证失败: 没有找到有效的数据表');
                    return false;
                }

                // 验证每个数据表的格式
                for (const key of dataKeys) {
                    if (!Array.isArray(data[key])) {
                        console.error(`数据验证失败: ${key} 不是数组格式`);
                        return false;
                    }
                }

                console.log('数据验证成功，找到以下数据表:', dataKeys);
                return true;

            } catch (error) {
                console.error('数据验证过程中出错:', error);
                return false;
            }
        }

        // 导入数据到数据库
        async function importDataToDatabase(data, progressFill, progressText) {
            // 过滤出数据表（排除timestamp和version）
            const dataKeys = Object.keys(data).filter(key =>
                key !== 'timestamp' && key !== 'version' && Array.isArray(data[key])
            );

            const totalTables = dataKeys.length;
            let importedTables = 0;

            // 数据表名映射到数据库函数
            const tableMapping = {
                'inputPersonnel': 'clearAndAddInputPersonnel',
                'salesAdvisors': 'clearAndAddSalesAdvisors',
                'carModels': 'clearAndAddCarModels',
                'intentions': 'clearAndAddIntentions',
                'competitors': 'clearAndAddCompetitors',
                'regions': 'clearAndAddRegions',
                'channels': 'clearAndAddChannels',
                'leadChannels': 'clearAndAddLeadChannels',
                'visitTypes': 'clearAndAddVisitTypes',
                'testDriveModels': 'clearAndAddTestDriveModels',
                'definitions': 'clearAndAddDefinitions',
                'customerManagement': 'clearAndAddCustomerManagement',
                'showroomEntries': 'clearAndAddShowroomEntries',
                'leadEntries': 'clearAndAddLeadEntries',
                'orderManagement': 'clearAndAddOrderManagement',
                'deliveryManagement': 'clearAndAddDeliveryManagement',
                'inventoryManagement': 'clearAndAddInventoryManagement',
                'partsSettings': 'clearAndAddPartsSettings',
                'partsInbound': 'clearAndAddPartsInbound',
                'partsOutbound': 'clearAndAddPartsOutbound',
                'partsLending': 'clearAndAddPartsLending'
            };

            for (const table of dataKeys) {
                if (Array.isArray(data[table])) {
                    progressText.textContent = `正在导入 ${table} 数据...`;

                    try {
                        // 使用对应的数据库函数导入数据
                        if (tableMapping[table] && window.dbFunctions[tableMapping[table]]) {
                            await window.dbFunctions[tableMapping[table]](data[table]);
                        } else {
                            // 如果没有专门的函数，尝试通用方法
                            console.warn(`没有找到 ${table} 的专用导入函数，跳过该表`);
                        }

                        importedTables++;
                        const progress = 20 + (importedTables / totalTables) * 70;
                        progressFill.style.width = `${progress}%`;

                        console.log(`成功导入 ${table} 数据，共 ${data[table].length} 条记录`);

                    } catch (error) {
                        console.error(`导入 ${table} 数据失败:`, error);
                        // 不抛出错误，继续导入其他表
                        console.warn(`跳过 ${table} 表的导入，继续处理其他表`);
                        importedTables++; // 仍然计入进度
                        const progress = 20 + (importedTables / totalTables) * 70;
                        progressFill.style.width = `${progress}%`;
                    }
                }
            }

            console.log(`数据导入完成，共处理 ${importedTables} 个数据表`);
        }

        // 刷新当前模块
        function refreshCurrentModule() {
            // 获取当前激活的标签页
            const activeTab = document.querySelector('.settings-tab.active');
            if (activeTab) {
                // 模拟点击当前标签页以刷新内容
                activeTab.click();
            } else {
                // 如果没有激活的标签页，则刷新整个页面
                location.reload();
            }
        }
    </script>
</body>
</html>
