// 扩展映射表最终测试 - 验证orderModule.js中的完整扩展映射表

// 从orderModule.js复制的完整扩展映射表（简化版用于测试）
function chineseToPinyin(chinese) {
    const pinyinFirstLetterMap = {
        // 扩展的常见姓氏
        '安': 'A', '艾': 'A', '敖': 'A', '鲍': 'B', '包': 'B', '白': 'B', '毕': 'B', '卜': 'B', '卞': 'B', '边': 'B', '薄': 'B',
        '陈': 'C', '曹': 'C', '蔡': 'C', '崔': 'C', '程': 'C', '常': 'C', '褚': 'C', '车': 'C', '成': 'C', '池': 'C', '楚': 'C',
        '丁': 'D', '邓': 'D', '杜': 'D', '董': 'D', '戴': 'D', '段': 'D', '窦': 'D', '邸': 'D', '刁': 'D', '狄': 'D',
        '冯': 'F', '付': 'F', '方': 'F', '范': 'F', '费': 'F', '傅': 'F', '樊': 'F', '封': 'F', '房': 'F', '丰': 'F', '凤': 'F',
        '郭': 'G', '高': 'G', '顾': 'G', '龚': 'G', '关': 'G', '甘': 'G', '葛': 'G', '耿': 'G', '宫': 'G', '巩': 'G', '古': 'G',
        '黄': 'H', '何': 'H', '胡': 'H', '韩': 'H', '侯': 'H', '贺': 'H', '郝': 'H', '洪': 'H', '华': 'H', '霍': 'H', '花': 'H',
        '江': 'J', '金': 'J', '姜': 'J', '蒋': 'J', '贾': 'J', '纪': 'J', '季': 'J', '焦': 'J', '靳': 'J', '景': 'J', '居': 'J',
        '孔': 'K', '康': 'K', '柯': 'K', '匡': 'K', '况': 'K', '邝': 'K', '寇': 'K', '库': 'K', '蒯': 'K', '阚': 'K',
        '李': 'L', '刘': 'L', '林': 'L', '梁': 'L', '罗': 'L', '陆': 'L', '卢': 'L', '吕': 'L', '廖': 'L', '雷': 'L', '黎': 'L',
        '龙': 'L', '娄': 'L', '柳': 'L', '路': 'L', '栗': 'L', '连': 'L', '蓝': 'L', '兰': 'L', '赖': 'L', '来': 'L', '乐': 'L',
        '马': 'M', '毛': 'M', '孟': 'M', '莫': 'M', '苗': 'M', '穆': 'M', '梅': 'M', '米': 'M', '闵': 'M', '明': 'M', '牟': 'M',
        '倪': 'N', '聂': 'N', '牛': 'N', '宁': 'N', '南': 'N', '年': 'N', '农': 'N', '那': 'N', '能': 'N', '乜': 'N', '钮': 'N',
        '欧': 'O', '欧阳': 'O', '区': 'O', '殴': 'O', '瓯': 'O',
        '潘': 'P', '彭': 'P', '裴': 'P', '皮': 'P', '平': 'P', '蒲': 'P', '濮': 'P', '朴': 'P', '逄': 'P', '盘': 'P', '庞': 'P',
        '邱': 'Q', '秦': 'Q', '钱': 'Q', '齐': 'Q', '乔': 'Q', '覃': 'Q', '祁': 'Q', '戚': 'Q', '强': 'Q', '屈': 'Q', '曲': 'Q',
        '任': 'R', '阮': 'R', '饶': 'R', '容': 'R', '荣': 'R', '冉': 'R', '芮': 'R', '汝': 'R', '茹': 'R', '瑞': 'R',
        '孙': 'S', '沈': 'S', '宋': 'S', '苏': 'S', '石': 'S', '史': 'S', '邵': 'S', '施': 'S', '司': 'S', '申': 'S', '盛': 'S',
        '唐': 'T', '田': 'T', '陶': 'T', '谭': 'T', '汤': 'T', '滕': 'T', '童': 'T', '涂': 'T', '屠': 'T', '谈': 'T', '台': 'T',
        '万': 'W', '王': 'W', '吴': 'W', '魏': 'W', '汪': 'W', '武': 'W', '韦': 'W', '温': 'W', '文': 'W', '翁': 'W', '邬': 'W',
        '许': 'X', '谢': 'X', '徐': 'X', '薛': 'X', '萧': 'X', '夏': 'X', '熊': 'X', '向': 'X', '邢': 'X', '辛': 'X', '席': 'X',
        '杨': 'Y', '叶': 'Y', '于': 'Y', '袁': 'Y', '姚': 'Y', '余': 'Y', '尹': 'Y', '闫': 'Y', '严': 'Y', '殷': 'Y', '易': 'Y',
        '张': 'Z', '赵': 'Z', '周': 'Z', '朱': 'Z', '郑': 'Z', '钟': 'Z', '曾': 'Z', '左': 'Z', '邹': 'Z', '庄': 'Z', '卓': 'Z',

        // 扩展的常见名字字符
        '一': 'Y', '二': 'E', '三': 'S', '四': 'S', '五': 'W', '六': 'L', '七': 'Q', '八': 'B', '九': 'J', '十': 'S',
        '安': 'A', '爱': 'A', '奥': 'A', '昂': 'A', '傲': 'A', '澳': 'A', '艾': 'A', '阿': 'A', '啊': 'A', '按': 'A',
        '斌': 'B', '彬': 'B', '宾': 'B', '冰': 'B', '兵': 'B', '波': 'B', '博': 'B', '柏': 'B', '白': 'B', '百': 'B',
        '超': 'C', '成': 'C', '诚': 'C', '城': 'C', '晨': 'C', '辰': 'C', '春': 'C', '纯': 'C', '聪': 'C', '翠': 'C',
        '德': 'D', '东': 'D', '冬': 'D', '栋': 'D', '丹': 'D', '旦': 'D', '道': 'D', '达': 'D', '大': 'D', '代': 'D',
        '恩': 'E', '儿': 'E', '尔': 'E', '二': 'E', '峨': 'E', '娥': 'E', '额': 'E', '鹅': 'E', '俄': 'E', '饿': 'E',
        '芳': 'F', '方': 'F', '飞': 'F', '非': 'F', '菲': 'F', '斐': 'F', '丰': 'F', '风': 'F', '峰': 'F', '锋': 'F',
        '国': 'G', '光': 'G', '广': 'G', '贵': 'G', '桂': 'G', '功': 'G', '公': 'G', '刚': 'G', '钢': 'G', '高': 'G',
        '华': 'H', '花': 'H', '辉': 'H', '慧': 'H', '惠': 'H', '会': 'H', '海': 'H', '涵': 'H', '寒': 'H', '汉': 'H',
        '霞': 'H', '霖': 'H', '霏': 'H', '航': 'H', '杭': 'H', '行': 'H', '号': 'H', '好': 'H', '豪': 'H', '浩': 'H',
        '杰': 'J', '健': 'J', '建': 'J', '江': 'J', '金': 'J', '锦': 'J', '进': 'J', '晶': 'J', '静': 'J', '敬': 'J',
        '军': 'J', '君': 'J', '俊': 'J', '骏': 'J', '嘉': 'J', '佳': 'J', '家': 'J', '加': 'J', '甲': 'J', '价': 'J',
        '洁': 'J', '结': 'J', '节': 'J', '杰': 'J', '接': 'J', '街': 'J', '阶': 'J', '皆': 'J', '揭': 'J', '借': 'J',
        '康': 'K', '凯': 'K', '开': 'K', '科': 'K', '可': 'K', '克': 'K', '客': 'K', '课': 'K', '快': 'K', '宽': 'K',
        '丽': 'L', '利': 'L', '力': 'L', '立': 'L', '理': 'L', '礼': 'L', '李': 'L', '里': 'L', '离': 'L', '黎': 'L',
        '琳': 'L', '霖': 'L', '麟': 'L', '鳞': 'L', '凛': 'L', '吝': 'L', '磊': 'L', '雷': 'L', '类': 'L', '累': 'L',
        '明': 'M', '民': 'M', '敏': 'M', '名': 'M', '命': 'M', '鸣': 'M', '铭': 'M', '茗': 'M', '冥': 'M', '溟': 'M',
        '美': 'M', '梅': 'M', '媒': 'M', '煤': 'M', '没': 'M', '每': 'M', '妹': 'M', '魅': 'M', '门': 'M', '们': 'M',
        '娜': 'N', '那': 'N', '哪': 'N', '纳': 'N', '拿': 'N', '南': 'N', '男': 'N', '难': 'N', '囊': 'N', '挠': 'N',
        '平': 'P', '萍': 'P', '苹': 'P', '屏': 'P', '乒': 'P', '坪': 'P', '评': 'P', '凭': 'P', '瓶': 'P', '品': 'P',
        '鹏': 'P', '朋': 'P', '彭': 'P', '蓬': 'P', '棚': 'P', '硼': 'P', '篷': 'P', '膨': 'P', '澎': 'P', '捧': 'P',
        '奇': 'Q', '齐': 'Q', '其': 'Q', '起': 'Q', '气': 'Q', '器': 'Q', '千': 'Q', '前': 'Q', '钱': 'Q', '潜': 'Q',
        '青': 'Q', '轻': 'Q', '清': 'Q', '情': 'Q', '晴': 'Q', '氰': 'Q', '倾': 'Q', '卿': 'Q', '擎': 'Q', '琼': 'Q',
        '人': 'R', '仁': 'R', '任': 'R', '认': 'R', '忍': 'R', '韧': 'R', '刃': 'R', '润': 'R', '闰': 'R', '若': 'R',
        '瑞': 'R', '锐': 'R', '睿': 'R', '芮': 'R', '蕊': 'R', '蚋': 'R', '软': 'R', '阮': 'R', '蠕': 'R', '儒': 'R',
        '山': 'S', '水': 'S', '石': 'S', '树': 'S', '森': 'S', '松': 'S', '顺': 'S', '思': 'S', '四': 'S', '丝': 'S',
        '深': 'S', '神': 'S', '什': 'S', '身': 'S', '呻': 'S', '伸': 'S', '申': 'S', '绅': 'S', '审': 'S', '婶': 'S',
        '天': 'T', '田': 'T', '甜': 'T', '填': 'T', '挑': 'T', '条': 'T', '跳': 'T', '贴': 'T', '铁': 'T', '听': 'T',
        '庭': 'T', '停': 'T', '亭': 'T', '挺': 'T', '艇': 'T', '通': 'T', '同': 'T', '铜': 'T', '彤': 'T', '童': 'T',
        '文': 'W', '武': 'W', '伟': 'W', '维': 'W', '为': 'W', '位': 'W', '味': 'W', '谓': 'W', '尾': 'W', '纬': 'W',
        '晓': 'X', '小': 'X', '孝': 'X', '校': 'X', '肖': 'X', '消': 'X', '宵': 'X', '淆': 'X', '心': 'X', '新': 'X',
        '雅': 'Y', '亚': 'Y', '讶': 'Y', '焉': 'Y', '咽': 'Y', '阉': 'Y', '烟': 'Y', '淹': 'Y', '盐': 'Y', '严': 'Y',
        '宇': 'Y', '语': 'Y', '羽': 'Y', '玉': 'Y', '域': 'Y', '芋': 'Y', '郁': 'Y', '吁': 'Y', '遇': 'Y', '喻': 'Y',
        '瑜': 'Y', '榆': 'Y', '愚': 'Y', '雨': 'Y', '与': 'Y', '屿': 'Y', '禹': 'Y', '颖': 'Y', '硬': 'Y', '映': 'Y',
        '志': 'Z', '智': 'Z', '制': 'Z', '治': 'Z', '中': 'Z', '忠': 'Z', '钟': 'Z', '终': 'Z', '种': 'Z', '重': 'Z',
        '竹': 'Z', '逐': 'Z', '主': 'Z', '住': 'Z', '助': 'Z', '注': 'Z', '驻': 'Z', '抓': 'Z', '爪': 'Z', '拽': 'Z'
    };

    if (!chinese) return 'XXX';

    let result = '';
    for (let i = 0; i < Math.min(chinese.length, 3); i++) {
        const char = chinese[i];
        if (pinyinFirstLetterMap[char]) {
            result += pinyinFirstLetterMap[char];
        } else if (/[A-Za-z]/.test(char)) {
            result += char.toUpperCase();
        } else {
            result += 'X';
        }
    }

    return result.padEnd(3, 'X').substring(0, 3);
}

function generateOrderNumber(advisorName, orderDate) {
    const date = new Date(orderDate);
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const advisorCode = chineseToPinyin(advisorName);
    const sequence = '01';
    return `${year}${month}${day}${advisorCode}${sequence}`;
}

// 最终综合测试
console.log('=== 扩展映射表最终测试 ===');
console.log('');

const finalTestCases = [
    // 新增姓氏测试
    { name: '傅志强', expected: 'FZQ', category: '新增姓氏' },
    { name: '邢美丽', expected: 'XML', category: '新增姓氏' },
    { name: '卜小明', expected: 'BXM', category: '新增姓氏' },
    { name: '关羽', expected: 'GYX', category: '新增姓氏' },
    { name: '鲍鱼', expected: 'BYX', category: '新增姓氏' },
    { name: '包青天', expected: 'BQT', category: '新增姓氏' },
    { name: '左宗棠', expected: 'ZZT', category: '新增姓氏' },
    { name: '褚时健', expected: 'CSJ', category: '新增姓氏' },
    { name: '费玉清', expected: 'FYQ', category: '新增姓氏' },
    
    // 新增名字字符测试
    { name: '王鹏飞', expected: 'WPF', category: '新增名字字符' },
    { name: '李霞', expected: 'LHX', category: '新增名字字符' },
    { name: '张辉', expected: 'ZHX', category: '新增名字字符' },
    { name: '陈宇航', expected: 'CYH', category: '新增名字字符' },
    { name: '刘琳', expected: 'LLX', category: '新增名字字符' },
    { name: '杨瑶', expected: 'YYX', category: '新增名字字符' },
    { name: '赵璐', expected: 'ZLX', category: '新增名字字符' },
    { name: '孙婷', expected: 'STX', category: '新增名字字符' },
    { name: '周雯', expected: 'ZWX', category: '新增名字字符' },
    { name: '吴雅', expected: 'WYX', category: '新增名字字符' },
    
    // 原有功能验证
    { name: '林忍斌', expected: 'LRB', category: '原有功能' },
    { name: '沈洁娜', expected: 'SJN', category: '原有功能' },
    { name: '石晓瑜', expected: 'SXY', category: '原有功能' },
    { name: '许佳颖', expected: 'XJY', category: '原有功能' }
];

console.log('综合测试结果:');
console.log('姓名\t\t实际结果\t预期结果\t状态\t\t类别');
console.log('------------------------------------------------------------');

let categoryStats = {};
let totalCorrect = 0;

finalTestCases.forEach(testCase => {
    const result = chineseToPinyin(testCase.name);
    const isCorrect = result === testCase.expected;
    const status = isCorrect ? '✓ 正确' : '✗ 错误';
    
    if (isCorrect) totalCorrect++;
    
    // 统计分类结果
    if (!categoryStats[testCase.category]) {
        categoryStats[testCase.category] = { total: 0, correct: 0 };
    }
    categoryStats[testCase.category].total++;
    if (isCorrect) categoryStats[testCase.category].correct++;
    
    console.log(`${testCase.name}\t\t${result}\t\t${testCase.expected}\t\t${status}\t\t${testCase.category}`);
});

console.log('');
console.log('=== 分类统计 ===');
Object.keys(categoryStats).forEach(category => {
    const stats = categoryStats[category];
    const percentage = ((stats.correct / stats.total) * 100).toFixed(1);
    console.log(`${category}: ${stats.correct}/${stats.total} (${percentage}%)`);
});

console.log('');
console.log('=== 总体统计 ===');
const totalPercentage = ((totalCorrect / finalTestCases.length) * 100).toFixed(1);
console.log(`总体通过率: ${totalCorrect}/${finalTestCases.length} (${totalPercentage}%)`);

// 订单号生成测试
console.log('');
console.log('订单号生成测试（前10个案例）:');
console.log('姓名\t\t拼音代码\t订单号\t\t\t长度\t状态');
console.log('--------------------------------------------------------');

const testDate = '2025-07-26';
let orderCorrect = 0;

finalTestCases.slice(0, 10).forEach(testCase => {
    const pinyinCode = chineseToPinyin(testCase.name);
    const orderNumber = generateOrderNumber(testCase.name, testDate);
    const expectedOrderNumber = `250726${testCase.expected}01`;
    const isCorrect = orderNumber === expectedOrderNumber && orderNumber.length === 11;
    const status = isCorrect ? '✓ 正确' : '✗ 错误';
    
    if (isCorrect) orderCorrect++;
    
    console.log(`${testCase.name}\t\t${pinyinCode}\t\t${orderNumber}\t${orderNumber.length}位\t${status}`);
});

console.log(`\n订单号生成通过率: ${orderCorrect}/10 (${(orderCorrect/10*100).toFixed(1)}%)`);

console.log('');
if (totalPercentage >= 95) {
    console.log('🎉 扩展映射表测试优秀！覆盖率达到预期目标。');
} else if (totalPercentage >= 85) {
    console.log('✅ 扩展映射表测试良好！大部分功能正常。');
} else {
    console.log('⚠️  扩展映射表需要进一步优化。');
}

console.log('');
console.log('=== 扩展映射表最终测试完成 ===');
