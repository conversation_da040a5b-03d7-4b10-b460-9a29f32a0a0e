<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入导出功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            color: #333;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        .field-list {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 10px 0;
        }
        .field-item {
            padding: 5px;
            background: #f8f9fa;
            border-radius: 3px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>导入导出功能测试</h1>
        <p>此页面用于测试展厅录入和线索录入模块的导入导出功能修复情况。</p>
    </div>

    <div class="test-container">
        <div class="test-section">
            <div class="test-title">1. 展厅录入导出功能测试</div>
            <p>测试导出功能是否包含所有必要字段（按正确顺序）：</p>
            <div class="field-list">
                <div class="field-item">录入日期</div>
                <div class="field-item">录入人员</div>
                <div class="field-item">销售顾问</div>
                <div class="field-item">来店时间</div>
                <div class="field-item">离店时间</div>
                <div class="field-item">滞店时间</div>
                <div class="field-item">来店类型</div>
                <div class="field-item">来店渠道</div>
                <div class="field-item">客户名称</div>
                <div class="field-item">性别</div>
                <div class="field-item">电话</div>
                <div class="field-item">意向车型</div>
                <div class="field-item">意向</div>
                <div class="field-item">区域</div>
                <div class="field-item">现有车型</div>
                <div class="field-item">对比车型</div>
                <div class="field-item">金融</div>
                <div class="field-item">置换</div>
                <div class="field-item">试驾</div>
                <div class="field-item">备注</div>
            </div>
            <button onclick="testShowroomExport()">测试展厅录入导出</button>
            <div id="showroom-export-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 展厅录入导入功能测试</div>
            <p>测试以下字段的导入映射是否正确：</p>
            <div class="field-list">
                <div class="field-item">来店渠道（下拉选择）</div>
                <div class="field-item">性别（男/女选择）</div>
                <div class="field-item">意向车型（车型选择）</div>
                <div class="field-item">现有车型（车型选择）</div>
                <div class="field-item">对比车型（车型选择）</div>
                <div class="field-item">置换（是/否选择）</div>
                <div class="field-item">试驾（是/否选择）</div>
                <div class="field-item">备注（文本字段）</div>
            </div>
            <input type="file" id="showroom-import-file" accept=".csv,.xlsx" />
            <button onclick="testShowroomImport()">测试展厅录入导入</button>
            <div id="showroom-import-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 线索录入导出功能测试</div>
            <p>测试线索录入导出功能是否包含所有必要字段（包括新增的"是否重复"字段）：</p>
            <div class="field-list">
                <div class="field-item">录入日期</div>
                <div class="field-item">是否有效</div>
                <div class="field-item">是否重复 <span style="color: red;">(新增)</span></div>
                <div class="field-item">客户名称</div>
                <div class="field-item">电话</div>
                <div class="field-item">线索ID</div>
                <div class="field-item">智慧号</div>
                <div class="field-item">意向车型</div>
                <div class="field-item">区域</div>
                <div class="field-item">微信</div>
                <div class="field-item">渠道</div>
                <div class="field-item">到店日期</div>
                <div class="field-item">成交日期</div>
                <div class="field-item">转销售跟进</div>
                <div class="field-item">接待顾问</div>
                <div class="field-item">首次跟进日期</div>
                <div class="field-item">跟进情况</div>
            </div>
            <button onclick="testLeadsExport()">测试线索录入导出</button>
            <div id="leads-export-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 线索录入导入功能测试</div>
            <p>测试线索录入导入功能是否正确处理所有字段（包括新增的"是否重复"字段）：</p>
            <input type="file" id="leads-import-file" accept=".csv,.xlsx" />
            <button onclick="testLeadsImport()">测试线索录入导入</button>
            <div id="leads-import-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">5. 分页功能测试</div>
            <p>测试展厅录入和线索录入列表的分页功能：</p>
            <button onclick="testPagination()">测试分页功能</button>
            <div id="pagination-result" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script src="database.js"></script>
    <script src="customerModule.js"></script>
    <script src="xlsx.full.min.js"></script>
    <script>
        // 初始化数据库
        async function initDatabase() {
            try {
                await initDB();
                console.log('数据库初始化成功');
            } catch (error) {
                console.error('数据库初始化失败:', error);
            }
        }

        // 测试展厅录入导出功能
        async function testShowroomExport() {
            const resultDiv = document.getElementById('showroom-export-result');
            resultDiv.style.display = 'block';
            
            try {
                // 检查导出功能是否存在
                if (typeof window.customerFunctions === 'undefined') {
                    throw new Error('customerFunctions 未定义');
                }
                
                if (typeof window.customerFunctions.exportShowroomToExcel !== 'function') {
                    throw new Error('exportShowroomToExcel 函数不存在');
                }
                
                // 创建测试数据
                const testData = [{
                    entryDate: '2025-01-22',
                    entryPersonnel: '测试人员',
                    salesAdvisor: '测试顾问',
                    arrivalTime: '09:00',
                    departureTime: '10:30',
                    stayDuration: '1小时30分钟',
                    visitType: '首次到店',
                    channel: '网络平台',
                    customerName: '测试客户',
                    gender: '男',
                    phone: '13800138000',
                    intendedModels: '测试车型',
                    intention: '购买意向',
                    region: '测试区域',
                    currentModel: '现有车型',
                    competitorModel: '对比车型',
                    finance: '是',
                    tradeIn: '否',
                    testDrive: '是',
                    notes: '测试备注'
                }];
                
                // 设置测试数据
                window.customerFunctions.allShowroomEntries = testData;
                
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = '✓ 展厅录入导出功能测试通过<br>- 导出函数存在<br>- 测试数据已准备<br>- 可以手动点击导出按钮进行完整测试';
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '✗ 展厅录入导出功能测试失败: ' + error.message;
            }
        }

        // 测试展厅录入导入功能
        async function testShowroomImport() {
            const resultDiv = document.getElementById('showroom-import-result');
            const fileInput = document.getElementById('showroom-import-file');
            
            resultDiv.style.display = 'block';
            
            if (!fileInput.files[0]) {
                resultDiv.className = 'test-result info';
                resultDiv.innerHTML = 'ℹ 请选择一个CSV或Excel文件进行导入测试';
                return;
            }
            
            try {
                // 检查导入功能是否存在
                if (typeof window.customerFunctions.mapImportFields !== 'function') {
                    throw new Error('mapImportFields 函数不存在');
                }
                
                // 测试字段映射
                const testRow = {
                    '客户名称': '测试客户',
                    '电话': '13800138000',
                    '来店渠道': '网络平台',
                    '性别': '男',
                    '意向车型': '测试车型',
                    '现有车型': '现有车型',
                    '对比车型': '对比车型',
                    '置换': '是',
                    '试驾': '否',
                    '备注': '测试备注'
                };
                
                const mappedData = window.customerFunctions.mapImportFields(testRow, 'showroomEntries');
                
                // 验证映射结果
                const requiredFields = ['customerName', 'phone', 'channel', 'gender', 'intendedModels', 'currentModel', 'competitorModel', 'tradeIn', 'testDrive', 'notes'];
                const missingFields = requiredFields.filter(field => !mappedData.hasOwnProperty(field));
                
                if (missingFields.length > 0) {
                    throw new Error('字段映射缺失: ' + missingFields.join(', '));
                }
                
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = '✓ 展厅录入导入功能测试通过<br>- 字段映射函数正常<br>- 所有必要字段都已映射<br>- 布尔值字段处理正确';
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '✗ 展厅录入导入功能测试失败: ' + error.message;
            }
        }

        // 测试线索录入导出功能
        async function testLeadsExport() {
            const resultDiv = document.getElementById('leads-export-result');
            resultDiv.style.display = 'block';
            
            try {
                // 检查导出功能是否存在
                if (typeof window.customerFunctions.exportLeadsToExcel !== 'function') {
                    throw new Error('exportLeadsToExcel 函数不存在');
                }
                
                // 创建测试数据（包含新增的isDuplicate字段）
                const testData = [{
                    entryDate: '2025-01-22',
                    isValid: true,
                    isDuplicate: false,
                    customerName: '测试客户',
                    phone: '13800138000',
                    leadId: '123456',
                    smartNumber: '789012',
                    intendedModels: '测试车型',
                    region: '测试区域',
                    wechat: '是',
                    channel: '测试渠道',
                    visitDate: '2025-01-23',
                    dealDate: '',
                    salesFollow: '测试顾问',
                    receptionAdvisor: '接待顾问',
                    firstFollowDate: '2025-01-24',
                    followStatus: '跟进中'
                }];
                
                // 设置测试数据
                window.customerFunctions.allLeadEntries = testData;
                
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = '✓ 线索录入导出功能测试通过<br>- 导出函数存在<br>- 包含"是否重复"字段<br>- 测试数据已准备';
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '✗ 线索录入导出功能测试失败: ' + error.message;
            }
        }

        // 测试线索录入导入功能
        async function testLeadsImport() {
            const resultDiv = document.getElementById('leads-import-result');
            resultDiv.style.display = 'block';
            
            try {
                // 测试字段映射（包含新增的isDuplicate字段）
                const testRow = {
                    '录入日期': '2025-01-22',
                    '是否有效': '是',
                    '是否重复': '否',
                    '客户名称': '测试客户',
                    '电话': '13800138000',
                    '线索ID': '123456',
                    '智慧号': '789012',
                    '意向车型': '测试车型',
                    '区域': '测试区域',
                    '微信': '是',
                    '渠道': '测试渠道'
                };
                
                const mappedData = window.customerFunctions.mapImportFields(testRow, 'leadEntries');
                
                // 验证映射结果（包含新增字段）
                const requiredFields = ['entryDate', 'isValid', 'isDuplicate', 'customerName', 'phone', 'leadId', 'smartNumber', 'intendedModels', 'region', 'wechat', 'channel'];
                const missingFields = requiredFields.filter(field => !mappedData.hasOwnProperty(field));
                
                if (missingFields.length > 0) {
                    throw new Error('字段映射缺失: ' + missingFields.join(', '));
                }
                
                // 验证isDuplicate字段处理
                if (typeof mappedData.isDuplicate !== 'boolean') {
                    throw new Error('isDuplicate字段类型错误，应为boolean');
                }
                
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = '✓ 线索录入导入功能测试通过<br>- 字段映射函数正常<br>- 包含"是否重复"字段<br>- 布尔值字段处理正确';
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '✗ 线索录入导入功能测试失败: ' + error.message;
            }
        }

        // 测试分页功能
        async function testPagination() {
            const resultDiv = document.getElementById('pagination-result');
            resultDiv.style.display = 'block';
            
            try {
                // 检查分页配置是否存在
                if (!window.customerFunctions.showroomPagination) {
                    throw new Error('展厅录入分页配置不存在');
                }
                
                if (!window.customerFunctions.leadsPagination) {
                    throw new Error('线索录入分页配置不存在');
                }
                
                // 检查分页函数是否存在
                const paginationFunctions = [
                    'renderShowroomPagination',
                    'renderLeadsPagination',
                    'goToShowroomPage',
                    'goToLeadsPage',
                    'jumpToShowroomPage',
                    'jumpToLeadsPage'
                ];
                
                const missingFunctions = paginationFunctions.filter(func => 
                    typeof window.customerFunctions[func] !== 'function'
                );
                
                if (missingFunctions.length > 0) {
                    throw new Error('分页函数缺失: ' + missingFunctions.join(', '));
                }
                
                // 测试分页配置
                const showroomConfig = window.customerFunctions.showroomPagination;
                const leadsConfig = window.customerFunctions.leadsPagination;
                
                if (showroomConfig.pageSize !== 50 || leadsConfig.pageSize !== 50) {
                    throw new Error('分页大小配置错误，应为50条/页');
                }
                
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = '✓ 分页功能测试通过<br>- 分页配置正确（50条/页）<br>- 所有分页函数存在<br>- 支持页码导航和跳转功能';
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '✗ 分页功能测试失败: ' + error.message;
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', async () => {
            await initDatabase();
            console.log('测试页面加载完成');
        });
    </script>
</body>
</html>
