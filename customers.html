<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>客户管理 - 汽车销售漏斗管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dexie@3.2.2/dist/dexie.min.js"></script>
    <script src="xlsx.full.min.js"></script>
    <link rel="stylesheet" href="styles.css" />
    <style>
        /* 客户管理标签页样式 */
        .customer-tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
        }

        .customer-tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #6c757d;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .customer-tab:hover {
            color: #4361ee;
            background-color: #f8f9fa;
        }

        .customer-tab.active {
            color: #4361ee;
            border-bottom-color: #4361ee;
            background-color: #f8f9fa;
        }

        .tab-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-header h2 {
            margin: 0;
            color: #212529;
            font-size: 18px;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .data-table td {
            font-size: 14px;
            color: #212529;
        }

        .data-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status.valid {
            background-color: #d4edda;
            color: #155724;
        }

        .status.invalid {
            background-color: #f8d7da;
            color: #721c24;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            margin-right: 4px;
        }

        .chart-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .chart-section h3 {
            margin: 0 0 15px 0;
            color: #212529;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            max-width: 900px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .modal-header h3 {
            margin: 0;
            color: #212529;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #6c757d;
        }

        .modal-close:hover {
            color: #212529;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #212529;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4361ee;
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        /* 详情显示样式 */
        .showroom-details .detail-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .showroom-details .detail-group label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .showroom-details .detail-group span {
            color: #212529;
            font-size: 14px;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .detail-actions {
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
        }

        .status-warning {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-secondary {
            background-color: #e2e3e5;
            color: #383d41;
        }

        .status-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body style="height: 100vh; margin: 0;">
    <div class="container" style="flex: 1; display: flex; flex-direction: column; height: 100%;">
        <header style="background: linear-gradient(135deg, #4361ee, #3f37c9); color: white; padding: 15px 30px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
            <div class="logo" style="display: flex; align-items: center; gap: 15px;">
                <i class="fas fa-users" style="font-size: 28px;"></i>
                <h1 style="font-size: 22px; font-weight: 600;">客户管理</h1>
            </div>
            <div class="user-info" style="display: flex; align-items: center; gap: 12px;">
                <div class="user-avatar" style="width: 42px; height: 42px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 18px;">张</div>
                <div>
                    <div>张经理</div>
                    <div style="font-size: 13px; opacity: 0.8;">销售主管</div>
                </div>
            </div>
        </header>
        
        <div class="main-content" style="flex: 1; display: flex; overflow: hidden;">
            <div class="sidebar" style="width: 260px; background: white; padding: 25px 20px; display: flex; flex-direction: column; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); z-index: 10; height: 100%; overflow-y: auto;">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 13px; color: #6c757d; margin-bottom: 5px;">客户总数</div>
                    <div style="font-size: 28px; font-weight: 700; color: #3f37c9;" id="total-customers">0</div>
                    <div style="font-size: 14px; color: #6c757d;">管理客户</div>
                </div>
                
                <ul class="nav-menu" style="list-style: none; margin-top: 20px;">
                    <li style="margin-bottom: 8px;"><a href="index.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-home" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 首页</a></li>
                    <li style="margin-bottom: 8px;"><a href="salesanalytics.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-chart-line" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 销售分析</a></li>
                    <li style="margin-bottom: 8px;"><a href="customers.html" class="active" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #4361ee; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users" style="margin-right: 12px; width: 20px; text-align: center; color: #4361ee;"></i> 客户管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="order.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-shopping-cart" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 订单管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="parts.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-boxes" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 配件库管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="settings.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 系统设置</a></li>
                    <li style="margin-bottom: 8px;"><a href="user.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 用户管理</a></li>
                </ul>
                
                <div style="margin-top: 30px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h3 style="font-size: 15px; margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-filter" style="color: #3f37c9; font-size: 12px;"></i> 客户筛选
                    </h3>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <button class="btn btn-outline filter-btn" data-filter="all">全部客户</button>
                        <button class="btn btn-outline filter-btn" data-filter="first-time">首次客户</button>
                        <button class="btn btn-outline filter-btn" data-filter="return">再次客户</button>
                        <button class="btn btn-outline filter-btn" data-filter="test-drive">试驾客户</button>
                        <button class="btn btn-outline filter-btn" data-filter="quote">报价客户</button>
                        <button class="btn btn-outline filter-btn" data-filter="deal">成交客户</button>
                    </div>
                </div>
            </div>
            
            <div class="content-area" style="flex: 1; padding: 30px; overflow-y: auto;">
                <div class="module-container active" id="customers-module">
                    <!-- 内容由customerModule.js动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="database.js"></script>
    <script src="notification.js"></script>
    <script src="customerModule.js"></script>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 等待所有脚本加载完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 检查必要的函数是否存在
                if (!window.dbFunctions) {
                    throw new Error('数据库函数未加载');
                }

                if (!window.customerFunctions) {
                    throw new Error('客户模块未加载');
                }

                // 初始化数据库
                await window.dbFunctions.initDB();

                // 加载客户模块
                await window.customerFunctions.loadCustomers();

                console.log('客户管理页面初始化完成');
            } catch (error) {
                console.error('初始化失败:', error);
                if (window.showNotification) {
                    showNotification('初始化失败', '系统初始化时出错: ' + error.message, 'danger');
                } else {
                    alert('初始化失败: ' + error.message);
                }
            }
        });

        // 全局函数：查看展厅录入详情
        function viewShowroomEntry(entryId) {
            if (window.customerFunctions && window.customerFunctions.showShowroomDetails) {
                window.customerFunctions.showShowroomDetails(entryId);
            } else {
                alert('功能暂不可用');
            }
        }

        // 全局函数：查看线索录入详情
        function viewLeadEntry(entryId) {
            if (window.customerFunctions && window.customerFunctions.showLeadDetails) {
                window.customerFunctions.showLeadDetails(entryId);
            } else {
                alert('功能暂不可用');
            }
        }
    </script>
</body>
</html>
