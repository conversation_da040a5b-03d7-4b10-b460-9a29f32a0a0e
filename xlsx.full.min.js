// 简化版的XLSX库，用于基本的Excel导入导出功能
if(typeof window !== 'undefined') {
    window.XLSX = {
        version: "0.20.1",
        utils: {
            json_to_sheet: function(data) {
                var ws = {};
                var range = {s: {c:10000000, r:10000000}, e: {c:0, r:0}};

                if(!data || data.length === 0) {
                    ws['!ref'] = 'A1:A1';
                    return ws;
                }

                var headers = Object.keys(data[0]);

                // 设置表头
                for(var C = 0; C < headers.length; ++C) {
                    if(range.s.r > 0) range.s.r = 0;
                    if(range.s.c > C) range.s.c = C;
                    if(range.e.r < 0) range.e.r = 0;
                    if(range.e.c < C) range.e.c = C;

                    var cell_ref = this.encode_cell({c:C, r:0});
                    ws[cell_ref] = {v: headers[C], t: 's'};
                }

                // 设置数据行
                for(var R = 0; R < data.length; ++R) {
                    for(var C = 0; C < headers.length; ++C) {
                        if(range.s.r > R + 1) range.s.r = R + 1;
                        if(range.s.c > C) range.s.c = C;
                        if(range.e.r < R + 1) range.e.r = R + 1;
                        if(range.e.c < C) range.e.c = C;

                        var cell_ref = this.encode_cell({c:C, r:R + 1});
                        var cell_value = data[R][headers[C]];

                        if(cell_value == null) continue;

                        var cell = {v: cell_value};
                        if(typeof cell_value === 'number') cell.t = 'n';
                        else if(typeof cell_value === 'boolean') cell.t = 'b';
                        else if(cell_value instanceof Date) {
                            cell.t = 'n'; cell.z = 'm/d/yy';
                            cell.v = this.datenum(cell_value);
                        }
                        else cell.t = 's';

                        ws[cell_ref] = cell;
                    }
                }

                if(range.s.c < 10000000) ws['!ref'] = this.encode_range(range);
                return ws;
            },

            sheet_to_json: function(worksheet, opts) {
                var o = opts || {};
                var range = this.decode_range(worksheet['!ref']);
                var out = [];
                var header = [];

                // 获取表头
                for(var C = range.s.c; C <= range.e.c; ++C) {
                    var cell_ref = this.encode_cell({c:C, r:range.s.r});
                    var cell = worksheet[cell_ref];
                    header[C] = cell ? cell.v : 'UNKNOWN_' + C;
                }

                // 获取数据行
                for(var R = range.s.r + 1; R <= range.e.r; ++R) {
                    var row = {};
                    for(var C = range.s.c; C <= range.e.c; ++C) {
                        var cell_ref = this.encode_cell({c:C, r:R});
                        var cell = worksheet[cell_ref];
                        row[header[C]] = cell ? cell.v : null;
                    }
                    out.push(row);
                }
                return out;
            },

            encode_cell: function(cell) {
                return this.encode_col(cell.c) + this.encode_row(cell.r);
            },

            encode_col: function(col) {
                var s = "";
                for(++col; col; col = Math.floor((col-1)/26)) {
                    s = String.fromCharCode(((col-1)%26) + 65) + s;
                }
                return s;
            },

            encode_row: function(row) {
                return "" + (row + 1);
            },

            encode_range: function(range) {
                return this.encode_cell(range.s) + ":" + this.encode_cell(range.e);
            },

            decode_range: function(range) {
                var idx = range.indexOf(":");
                if(idx == -1) return {s: this.decode_cell(range), e: this.decode_cell(range)};
                return {s: this.decode_cell(range.slice(0, idx)), e: this.decode_cell(range.slice(idx + 1))};
            },

            decode_cell: function(cell) {
                var i = 0;
                for(; i < cell.length && (cell.charCodeAt(i) < 48 || cell.charCodeAt(i) > 57); ++i);
                return {c: this.decode_col(cell.slice(0, i)), r: this.decode_row(cell.slice(i))};
            },

            decode_col: function(col) {
                var c = 0;
                for(var i = 0; i < col.length; ++i) {
                    c = 26 * c + col.charCodeAt(i) - 64;
                }
                return c - 1;
            },

            decode_row: function(row) {
                return parseInt(row, 10) - 1;
            },

            datenum: function(date) {
                return (date.getTime() - new Date(1899, 11, 30).getTime()) / (24 * 60 * 60 * 1000);
            },

            book_new: function() {
                return {SheetNames: [], Sheets: {}};
            },

            book_append_sheet: function(wb, ws, name) {
                if(!name) name = 'Sheet' + (wb.SheetNames.length + 1);
                wb.SheetNames.push(name);
                wb.Sheets[name] = ws;
            }
        },

        read: function(data, opts) {
            // 简化的读取功能 - 仅支持CSV格式
            var workbook = this.utils.book_new();
            var lines = data.split('\n');
            var jsonData = [];

            if(lines.length === 0) return workbook;

            var headers = lines[0].split(',');
            for(var i = 1; i < lines.length; i++) {
                if(lines[i].trim() === '') continue;
                var values = lines[i].split(',');
                var row = {};
                for(var j = 0; j < headers.length; j++) {
                    row[headers[j]] = values[j] || '';
                }
                jsonData.push(row);
            }

            var ws = this.utils.json_to_sheet(jsonData);
            this.utils.book_append_sheet(workbook, ws, 'Sheet1');
            return workbook;
        },

        write: function(workbook, opts) {
            opts = opts || {};

            if (opts.type === 'binary' || opts.bookType === 'xlsx') {
                return this._writeBinary(workbook, opts);
            }

            // 默认CSV格式
            var csv = '';
            var sheetName = workbook.SheetNames[0];
            var worksheet = workbook.Sheets[sheetName];

            if(!worksheet['!ref']) return '';

            var range = this.utils.decode_range(worksheet['!ref']);

            for(var R = range.s.r; R <= range.e.r; ++R) {
                var row = [];
                for(var C = range.s.c; C <= range.e.c; ++C) {
                    var cell_ref = this.utils.encode_cell({c:C, r:R});
                    var cell = worksheet[cell_ref];
                    var value = cell ? cell.v : '';
                    // 处理包含逗号的值
                    if(typeof value === 'string' && value.indexOf(',') !== -1) {
                        value = '"' + value.replace(/"/g, '""') + '"';
                    }
                    row.push(value);
                }
                csv += row.join(',') + '\n';
            }

            return csv;
        },

        _writeBinary: function(workbook, opts) {
            // 生成简化的Excel XML格式
            var sheetName = workbook.SheetNames[0];
            var worksheet = workbook.Sheets[sheetName];

            if (!worksheet['!ref']) return '';

            var range = this.utils.decode_range(worksheet['!ref']);
            var xml = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n';
            xml += '<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">\r\n';
            xml += '<sheetData>\r\n';

            for(var R = range.s.r; R <= range.e.r; ++R) {
                xml += '<row r="' + (R + 1) + '">\r\n';
                for(var C = range.s.c; C <= range.e.c; ++C) {
                    var cell_ref = this.utils.encode_cell({c:C, r:R});
                    var cell = worksheet[cell_ref];
                    if (cell) {
                        xml += '<c r="' + cell_ref + '" t="inlineStr"><is><t>' +
                               this._escapeXML(cell.v) + '</t></is></c>\r\n';
                    }
                }
                xml += '</row>\r\n';
            }

            xml += '</sheetData>\r\n';
            xml += '</worksheet>';

            return xml;
        },

        _escapeXML: function(str) {
            return String(str)
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;');
        },

        writeFile: function(workbook, filename, opts) {
            opts = opts || {};

            // 检查文件扩展名
            var isXlsx = filename.toLowerCase().endsWith('.xlsx');

            if (isXlsx) {
                // 生成真正的Excel文件
                this._writeXlsxFile(workbook, filename);
            } else {
                // 回退到CSV
                var data = this.write(workbook, opts);
                var blob = new Blob(['\ufeff' + data], {type: "text/csv;charset=utf-8;"});
                this._downloadFile(blob, filename);
            }
        },

        _writeXlsxFile: function(workbook, filename) {
            // 创建一个真正的Excel文件
            var sheetName = workbook.SheetNames[0];
            var worksheet = workbook.Sheets[sheetName];

            if (!worksheet['!ref']) {
                alert('没有数据可导出');
                return;
            }

            // 生成Excel兼容的HTML格式
            var html = this._generateExcelHTML(workbook);
            var blob = new Blob([html], {
                type: "application/vnd.ms-excel;charset=utf-8"
            });
            this._downloadFile(blob, filename);
        },

        _generateExcelHTML: function(workbook) {
            var sheetName = workbook.SheetNames[0];
            var worksheet = workbook.Sheets[sheetName];
            var range = this.utils.decode_range(worksheet['!ref']);

            var html = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
            html += '<head><meta charset="utf-8"><meta name="ProgId" content="Excel.Sheet"><meta name="Generator" content="Microsoft Excel 15"></head>';
            html += '<body><table border="1">';

            for(var R = range.s.r; R <= range.e.r; ++R) {
                html += '<tr>';
                for(var C = range.s.c; C <= range.e.c; ++C) {
                    var cell_ref = this.utils.encode_cell({c:C, r:R});
                    var cell = worksheet[cell_ref];
                    var value = cell ? this._escapeHTML(cell.v) : '';

                    // 为表头添加样式
                    if (R === range.s.r) {
                        html += '<th style="background-color:#4CAF50;color:white;font-weight:bold;text-align:center;">' + value + '</th>';
                    } else {
                        html += '<td>' + value + '</td>';
                    }
                }
                html += '</tr>';
            }

            html += '</table></body></html>';
            return html;
        },

        _escapeHTML: function(str) {
            return String(str)
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;');
        },

        _downloadFile: function(blob, filename) {
            var url = URL.createObjectURL(blob);
            var a = document.createElement("a");
            a.href = url;
            a.download = filename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    };
}
