# 汽车销售管理系统 V1.2 - 完整重构版

## 系统概述

本系统是一个基于Web的汽车销售管理系统，采用前端技术栈（HTML5、CSS3、JavaScript）和IndexedDB本地数据库，为汽车销售团队提供全面的客户管理、订单管理、用户管理和数据分析功能。

## 重构内容

### 1. 页面结构调整

#### 重命名页面
- `dashboard.html` → `salesanalytics.html` (控制面板 → 销售分析)
- `deals.html` → `order.html` (成交管理 → 订单管理)

#### 删除页面
- `testdrives.html` (试驾管理)
- `quotes.html` (报价管理)
- `analytics.html` (销售分析)
- `visit-entry.html` (到店录入)

#### 新增页面
- `user.html` (用户管理)

#### 保留页面
- `index.html` (首页)
- `customers.html` (客户管理) - 功能扩展
- `settings.html` (系统设置) - 试驾车型模块调整

### 2. 数据库结构重构

#### 新增数据表
- `showroomEntries` - 展厅录入数据
- `leadEntries` - 线索录入数据
- `orderManagement` - 订单管理数据
- `inventoryManagement` - 库存管理数据
- `deliveryTargets` - 提车目标数据
- `orderTargets` - 订单目标数据
- `retailTargets` - 零售目标数据
- `userManagement` - 用户管理数据

#### 删除数据表
- `testDrives` - 试驾数据
- `quotes` - 报价数据
- `deals` - 成交数据

#### 修改数据表
- `testDriveModels` - 扩展字段，支持完整的试驾车型管理

### 3. 功能模块重构

#### 客户管理 (customers.html)
- **展厅录入模块**：记录客户到店信息，包含录入日期、人员、销售顾问、来店时间等20个字段
- **线索录入模块**：管理销售线索，包含客户信息、线索来源、跟进状态等16个字段
- **客户列表模块**：原有客户管理功能保持不变

#### 订单管理 (order.html)
- **订单管理模块**：管理客户订单，包含订单状态、客户信息、车辆信息等17个字段
- **库存管理模块**：管理车辆库存，包含库存状态、车辆信息、库龄等15个字段
- **目标管理模块**：分为提车目标、订单目标、零售目标三个子模块

#### 用户管理 (user.html)
- **用户管理模块**：管理系统用户，包含账号、密码、权限、状态等10个字段
- **权限控制**：支持页面级权限管理，密码加密存储
- **用户状态管理**：支持启用/禁用用户状态

#### 系统设置 (settings.html)
- **试驾车型模块调整**：扩展为包含车牌、配置、车架号、外色、内饰、指导价、状态、类型等15个字段的完整管理

## 技术特性

### 前端技术
- **HTML5**: 语义化标记，响应式设计
- **CSS3**: 现代样式，Flexbox/Grid布局，动画效果
- **JavaScript ES6+**: 模块化开发，异步处理
- **Font Awesome**: 图标库
- **Chart.js**: 数据可视化

### 数据存储
- **Dexie.js**: IndexedDB封装库
- **本地存储**: 无需服务器，数据持久化
- **版本管理**: 数据库版本控制和迁移

### 架构设计
- **模块化**: 每个功能模块独立的JS文件
- **组件化**: 可复用的UI组件
- **响应式**: 适配不同屏幕尺寸
- **标签页**: 多功能模块的标签页切换

## 文件结构

```
V1.1FJ/
├── index.html              # 系统首页
├── salesanalytics.html     # 销售分析页面
├── customers.html          # 客户管理页面
├── order.html             # 订单管理页面
├── settings.html          # 系统设置页面
├── user.html              # 用户管理页面
├── test.html              # 功能测试页面
├── database.js            # 数据库操作模块（V5）
├── customerModule.js      # 客户管理模块（扩展）
├── orderModule.js         # 订单管理模块（重构）
├── userModule.js          # 用户管理模块（新增）
├── settingsModule.js      # 系统设置模块
├── notification.js        # 通知系统模块
├── styles.css             # 全局样式文件
├── app.js                 # 主应用脚本
└── README.md              # 系统说明文档
```

## 数据字段说明

### 展厅录入 (showroomEntries)
- 录入日期、录入人员、销售顾问、来店时间、离店时间、滞店时间
- 来店类型、来店渠道、客户名称、性别、电话、意向车型
- 意向、区域、现有车型、对比车型、金融、置换、试驾、备注

### 线索录入 (leadEntries)  
- 录入日期、是否有效、客户名称、电话、线索ID、智慧号
- 意向车型、区域、微信、渠道、到店日期、成交日期
- 转销售跟进、接待顾问、首次跟进日期、跟进情况

### 订单管理 (orderManagement)
- 序号、订单状态、订单日期、客户名称、联系手机1、联系手机2
- 审核状态、销售顾问、VIN、车型、配置、外色、内饰
- 选装件、交付状态、资源状态、交付日期

### 库存管理 (inventoryManagement)
- 序号、库存状态、车型、版本、车架号、外色、内饰
- 原厂选装、标准、位置、生产日期、发运日期、入库日期
- 库龄、指导价、备注

### 试驾车型 (testDriveModels)
- 序号、车牌、车型、配置、车架号、外色、内饰、原厂选装
- 指导价、状态、类型、开票日期、上报日期、登记日期、到期日期、备注

## 使用说明

### 系统启动
1. 直接打开 `index.html` 文件
2. 系统会自动初始化数据库
3. 通过导航菜单访问各个功能模块

### 功能测试
1. 打开 `test.html` 进行系统功能测试
2. 依次点击各个测试按钮验证功能
3. 查看测试结果确保系统正常运行

### 数据管理
- 所有数据存储在浏览器的IndexedDB中
- 支持数据的增删改查操作
- 具备表单验证和错误处理机制

## 浏览器兼容性

- Chrome 58+
- Firefox 55+
- Safari 11+
- Edge 79+

## 注意事项

1. **数据安全**: 数据存储在本地浏览器中，清除浏览器数据会导致数据丢失
2. **备份建议**: 定期使用系统设置中的数据导出功能进行备份
3. **性能优化**: 大量数据时建议定期清理历史数据
4. **浏览器限制**: IndexedDB有存储容量限制，注意数据量控制

## 更新日志

### V1.2 (2025-01-22)
- 完成系统完整架构重构
- 新增用户管理功能模块
- 完善展厅录入和线索录入功能
- 重构订单管理和库存管理
- 优化试驾车型管理
- 更新数据库结构至版本5
- 删除visit-entry页面，整合功能
- 改进用户界面和交互体验
- 实现完整的权限管理系统

---

**开发团队**: Augment Agent  
**最后更新**: 2025年1月22日
