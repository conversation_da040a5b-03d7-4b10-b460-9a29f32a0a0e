// 客户管理功能模块
window.customerFunctions = {
    allCustomers: [],
    allShowroomEntries: [],
    allLeadEntries: [],
    currentTab: 'showroom',

    // 分页配置
    showroomPagination: {
        currentPage: 1,
        pageSize: 50,
        totalPages: 1,
        totalRecords: 0
    },
    leadsPagination: {
        currentPage: 1,
        pageSize: 50,
        totalPages: 1,
        totalRecords: 0
    },

    // 列显示设置
    showroomColumnSettings: {
        entryDate: true,
        entryPersonnel: true,
        salesAdvisor: true,
        stayDuration: true,
        customerName: true,
        phone: true,
        visitType: true,
        region: true,
        intention: true,
        intendedModels: true,
        testDrive: true,
        finance: true,
        tradeIn: true,
        quote: true
    },

    leadsColumnSettings: {
        entryDate: true,
        isValid: true,
        isDuplicate: true,
        customerName: true,
        phone: true,
        leadId: true,
        smartNumber: true,
        intendedModels: true,
        region: true,
        wechat: true,
        channel: true,
        visitDate: true,
        dealDate: true,
        salesFollow: true,
        receptionAdvisor: true,
        firstFollowDate: true,
        followStatus: true
    },

    // 加载客户管理模块
    loadCustomers: async function() {
        try {
            // 加载列设置
            this.loadColumnSettings();

            this.allCustomers = await getAllCustomers();
            this.allShowroomEntries = await window.dbFunctions.getAllShowroomEntries();
            this.allLeadEntries = await window.dbFunctions.getAllLeadEntries();
            this.renderCustomerContent();
        } catch (error) {
            console.error('加载客户数据失败:', error);
        }
    },

    // 渲染客户管理内容
    renderCustomerContent: function() {
        const moduleContainer = document.getElementById('customers-module');
        if (!moduleContainer) return;

        moduleContainer.innerHTML = `
            <div class="module-header">
                <h1><i class="fas fa-users"></i> 客户管理</h1>
            </div>

            <div class="card">
                <div class="customer-tabs">
                    <div class="customer-tab ${this.currentTab === 'showroom' ? 'active' : ''}" data-tab="showroom">
                        <i class="fas fa-store"></i> 展厅录入
                    </div>
                    <div class="customer-tab ${this.currentTab === 'leads' ? 'active' : ''}" data-tab="leads">
                        <i class="fas fa-user-plus"></i> 线索录入
                    </div>
                    <div class="customer-tab ${this.currentTab === 'customers' ? 'active' : ''}" data-tab="customers">
                        <i class="fas fa-users"></i> 客户列表
                    </div>
                </div>

                <div class="customer-content" id="customer-content">
                    ${this.renderTabContent()}
                </div>
            </div>
        `;

        // 绑定事件
        this.bindEvents();
    },

    // 渲染标签页内容
    renderTabContent: function() {
        switch(this.currentTab) {
            case 'customers':
                return this.renderCustomersTab();
            case 'showroom':
                return this.renderShowroomTab();
            case 'leads':
                return this.renderLeadsTab();
            default:
                return this.renderShowroomTab();
        }
    },

    // 渲染客户列表标签页
    renderCustomersTab: function() {
        return `
            <div class="tab-header">
                <h2>客户列表 (${this.allCustomers.length})</h2>
                <div>
                    <input type="text" id="customer-search" placeholder="搜索客户..." style="padding: 8px; border-radius: 4px; border: 1px solid #ddd; margin-right: 10px;">
                    <button class="btn btn-primary" id="new-customer-btn">
                        <i class="fas fa-plus"></i> 新客户
                    </button>
                </div>
            </div>

            <div class="customer-list-content" id="all-customers">
                ${this.renderCustomerList(this.allCustomers)}
            </div>

            <div class="chart-section" style="margin-top: 30px;">
                <h3><i class="fas fa-chart-pie"></i> 客户状态分布</h3>
                <div class="chart-container">
                    <canvas id="customer-status-chart"></canvas>
                </div>
            </div>
        `;
    },

    // 渲染展厅录入标签页
    renderShowroomTab: function() {
        return `
            <div class="tab-header" style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #212529; font-size: 18px;">展厅录入 (${this.allShowroomEntries.length})</h2>
                <div style="display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">
                    <input type="text" id="showroom-search" placeholder="搜索客户姓名或电话..." style="padding: 8px 12px; border-radius: 4px; border: 1px solid #ddd; width: 200px;">
                    <button class="btn btn-outline" onclick="customerFunctions.showShowroomColumnSettings()" title="列设置" style="padding: 8px 12px;">
                        <i class="fas fa-columns"></i>
                    </button>
                    <button class="btn btn-primary" id="new-showroom-btn" style="padding: 8px 12px;">
                        <i class="fas fa-plus"></i> 新增展厅录入
                    </button>
                    <button class="btn btn-secondary" onclick="customerFunctions.importShowroomFromExcel()" style="padding: 8px 12px;">
                        <i class="fas fa-upload"></i> 导入
                    </button>
                    <button class="btn btn-secondary" onclick="customerFunctions.exportShowroomToExcel()" style="padding: 8px 12px;">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </div>
            <div class="filter-section" style="background: #f8f9fa; padding: 12px 16px; margin-bottom: 20px; border-radius: 6px; border: 1px solid #e9ecef;">
                <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
                    <div class="form-group" style="margin-bottom: 0; display: flex; align-items: center;">
                        <label style="margin-right: 8px; font-weight: 500; color: #495057; white-space: nowrap;">开始日期：</label>
                        <input type="date" id="showroom-start-date" style="padding: 6px 8px; border-radius: 4px; border: 1px solid #ced4da; font-size: 14px;">
                    </div>
                    <div class="form-group" style="margin-bottom: 0; display: flex; align-items: center;">
                        <label style="margin-right: 8px; font-weight: 500; color: #495057; white-space: nowrap;">结束日期：</label>
                        <input type="date" id="showroom-end-date" style="padding: 6px 8px; border-radius: 4px; border: 1px solid #ced4da; font-size: 14px;">
                    </div>
                    <button class="btn btn-primary" id="showroom-filter-btn" onclick="customerFunctions.filterShowroomByDate()" style="padding: 6px 16px; font-size: 14px;">
                        <i class="fas fa-filter"></i> 筛选
                    </button>
                    <button class="btn btn-secondary" id="showroom-reset-btn" onclick="customerFunctions.resetShowroomFilter()" style="padding: 6px 16px; font-size: 14px;">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
            </div>
            <input type="file" id="showroom-import-file" accept=".xlsx,.xls" style="display: none;" onchange="customerFunctions.handleShowroomImport(this)">
            <div class="showroom-list-content" id="all-showroom-entries">
                ${this.renderShowroomList(this.filteredShowroomEntries || this.allShowroomEntries)}
            </div>
        `;
    },

    // 渲染线索录入标签页
    renderLeadsTab: function() {
        return `
            <div class="tab-header" style="margin-bottom: 20px;">
                <h2 style="margin: 0; color: #212529; font-size: 18px;">线索录入 (${this.allLeadEntries.length})</h2>
                <div style="display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">
                    <input type="text" id="leads-search" placeholder="搜索客户姓名或电话..." style="padding: 8px 12px; border-radius: 4px; border: 1px solid #ddd; width: 200px;">
                    <button class="btn btn-outline" onclick="customerFunctions.showLeadsColumnSettings()" title="列设置" style="padding: 8px 12px;">
                        <i class="fas fa-columns"></i>
                    </button>
                    <button class="btn btn-primary" id="new-lead-btn" style="padding: 8px 12px;">
                        <i class="fas fa-plus"></i> 新增线索录入
                    </button>
                    <button class="btn btn-secondary" onclick="customerFunctions.importLeadsFromExcel()" style="padding: 8px 12px;">
                        <i class="fas fa-upload"></i> 导入
                    </button>
                    <button class="btn btn-secondary" onclick="customerFunctions.exportLeadsToExcel()" style="padding: 8px 12px;">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </div>
            <div class="filter-section" style="background: #f8f9fa; padding: 12px 16px; margin-bottom: 20px; border-radius: 6px; border: 1px solid #e9ecef;">
                <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
                    <div class="form-group" style="margin-bottom: 0; display: flex; align-items: center;">
                        <label style="margin-right: 8px; font-weight: 500; color: #495057; white-space: nowrap;">开始日期：</label>
                        <input type="date" id="leads-start-date" style="padding: 6px 8px; border-radius: 4px; border: 1px solid #ced4da; font-size: 14px;">
                    </div>
                    <div class="form-group" style="margin-bottom: 0; display: flex; align-items: center;">
                        <label style="margin-right: 8px; font-weight: 500; color: #495057; white-space: nowrap;">结束日期：</label>
                        <input type="date" id="leads-end-date" style="padding: 6px 8px; border-radius: 4px; border: 1px solid #ced4da; font-size: 14px;">
                    </div>
                    <button class="btn btn-primary" id="leads-filter-btn" onclick="customerFunctions.filterLeadsByDate()" style="padding: 6px 16px; font-size: 14px;">
                        <i class="fas fa-filter"></i> 筛选
                    </button>
                    <button class="btn btn-secondary" id="leads-reset-btn" onclick="customerFunctions.resetLeadsFilter()" style="padding: 6px 16px; font-size: 14px;">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
            </div>
            <input type="file" id="leads-import-file" accept=".xlsx,.xls" style="display: none;" onchange="customerFunctions.handleLeadsImport(this)">
            <div class="leads-list-content" id="all-lead-entries">
                ${this.renderLeadsList(this.filteredLeadEntries || this.allLeadEntries)}
            </div>
        `;
    },

    // 渲染客户列表
    renderCustomerList: function(customers) {
        if (customers.length === 0) {
            return '<div style="text-align: center; padding: 40px; color: var(--gray);">暂无客户数据</div>';
        }
        
        return customers.map(customer => `
            <div class="customer-item" data-customer-id="${customer.id}">
                <div class="customer-avatar">${customer.name.charAt(0)}</div>
                <div class="customer-info">
                    <h4>${customer.name}</h4>
                    <p>电话: ${customer.phone} | 车型: ${customer.carModel}</p>
                    <p style="font-size: 12px; color: var(--gray);">
                        到店时间: ${new Date(customer.date).toLocaleDateString('zh-CN')}
                    </p>
                </div>
                <div class="customer-status status-${this.getStatusClass(customer.status)}">
                    ${customer.status}
                </div>
                <div style="margin-left: 15px;">
                    <button class="btn btn-outline" onclick="editCustomer(${customer.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </div>
        `).join('');
    },
    
    // 筛选客户
    filterCustomers: function(status) {
        let filteredCustomers;
        
        if (status === 'all') {
            filteredCustomers = this.allCustomers;
        } else {
            filteredCustomers = this.allCustomers.filter(customer => customer.status === status);
        }
        
        // 更新客户列表
        const customerListContainer = document.getElementById('all-customers');
        if (customerListContainer) {
            customerListContainer.innerHTML = this.renderCustomerList(filteredCustomers);
        }
        
        // 更新列表标题
        const listHeader = document.querySelector('.customer-list-header div:first-child');
        if (listHeader) {
            listHeader.textContent = `客户列表 (${filteredCustomers.length})`;
        }
    },
    
    // 绑定事件
    bindEvents: function() {
        this.bindTabEvents();
    },

    // 绑定客户列表事件
    bindCustomerEvents: function() {
        // 搜索功能
        const searchInput = document.getElementById('customer-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const filteredCustomers = this.allCustomers.filter(customer =>
                    customer.name.toLowerCase().includes(searchTerm) ||
                    customer.phone.includes(searchTerm) ||
                    customer.carModel.toLowerCase().includes(searchTerm)
                );

                const customerListContainer = document.getElementById('all-customers');
                if (customerListContainer) {
                    customerListContainer.innerHTML = this.renderCustomerList(filteredCustomers);
                }
            });
        }

        // 新客户按钮
        const newCustomerBtn = document.getElementById('new-customer-btn');
        if (newCustomerBtn) {
            newCustomerBtn.addEventListener('click', () => {
                this.showNewCustomerForm();
            });
        }
    },
    
    // 显示新客户表单
    showNewCustomerForm: function() {
        // 这里可以实现新客户表单的显示逻辑
        alert('新客户功能开发中...');
    },

    // 显示展厅录入表单
    showShowroomEntryForm: async function(editId = null) {
        try {
            const inputPersonnel = await window.dbFunctions.getAllInputPersonnel();
            // 只获取在职的销售顾问用于新增，编辑时获取所有销售顾问以保持历史数据
            const salesAdvisors = editId ?
                await window.dbFunctions.getAllSalesAdvisors() :
                await window.dbFunctions.getActiveSalesAdvisors();
            const visitTypes = await window.dbFunctions.getAllVisitTypes();
            const channels = await window.dbFunctions.getAllChannels();
            const carModels = await window.dbFunctions.getAllCarModels();
            const intentions = await window.dbFunctions.getAllIntentions();
            const regions = await window.dbFunctions.getAllRegions();
            const competitors = await window.dbFunctions.getAllCompetitors();
            const testDriveModels = await window.dbFunctions.getAllTestDriveModels();

            let entry = {
                entryDate: new Date().toISOString().split('T')[0],
                entryPersonnel: '',
                salesAdvisor: '',
                arrivalTime: '',
                departureTime: '',
                stayDuration: '',
                visitType: '',
                channel: '',
                customerName: '',
                gender: '男',
                phone: '',
                intendedModels: '',
                intention: '',
                region: '',
                currentModel: '',
                competitorModel: '',
                finance: '否',
                tradeIn: '否',
                testDrive: '',
                quote: '',
                quoteAmount: '',
                notes: ''
            };

            if (editId !== null) {
                entry = await window.dbFunctions.getShowroomEntryById(editId) || entry;
            }

            this.showModal('展厅录入', this.renderShowroomEntryForm(entry, {
                inputPersonnel, salesAdvisors, visitTypes, channels, carModels,
                intentions, regions, competitors, testDriveModels
            }, editId));
        } catch (error) {
            console.error('显示展厅录入表单失败:', error);
            alert('显示展厅录入表单失败: ' + error.message);
        }
    },

    // 显示线索录入表单
    showLeadEntryForm: async function(editId = null) {
        try {
            const carModels = await window.dbFunctions.getAllCarModels();
            const regions = await window.dbFunctions.getAllRegions();
            const leadChannels = await window.dbFunctions.getAllLeadChannels();
            // 只获取在职的销售顾问用于新增，编辑时获取所有销售顾问以保持历史数据
            const salesAdvisors = editId ?
                await window.dbFunctions.getAllSalesAdvisors() :
                await window.dbFunctions.getActiveSalesAdvisors();

            let entry = {
                entryDate: new Date().toISOString().split('T')[0],
                isValid: true,
                isDuplicate: false,
                customerName: '',
                phone: '',
                leadId: '',
                smartNumber: '',
                intendedModels: '',
                region: '',
                wechat: '否',
                channel: '',
                visitDate: '',
                dealDate: '',
                salesFollow: '',
                receptionAdvisor: '',
                firstFollowDate: '',
                followStatus: ''
            };

            if (editId !== null) {
                entry = await window.dbFunctions.getLeadEntryById(editId) || entry;
            }

            this.showModal('线索录入', this.renderLeadEntryForm(entry, {
                carModels, regions, leadChannels, salesAdvisors
            }, editId));
        } catch (error) {
            console.error('显示线索录入表单失败:', error);
            alert('显示线索录入表单失败: ' + error.message);
        }
    },
    
    // 渲染状态图表
    renderStatusChart: function(customers) {
        const canvas = document.getElementById('customer-status-chart');
        if (!canvas) return;
        
        const statusCounts = customers.reduce((acc, customer) => {
            acc[customer.status] = (acc[customer.status] || 0) + 1;
            return acc;
        }, {});
        
        const ctx = canvas.getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(statusCounts),
                datasets: [{
                    data: Object.values(statusCounts),
                    backgroundColor: [
                        '#3498db',
                        '#f39c12', 
                        '#2ecc71',
                        '#27ae60',
                        '#e74c3c'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    },
    
    // 获取状态类名
    getStatusClass: function(status) {
        const statusMap = {
            '初次接触': 'new',
            '意向客户': 'test-drive',
            '试驾客户': 'quote',
            '成交客户': 'deal',
            '已流失': 'lost'
        };
        return statusMap[status] || 'new';
    },

    // 渲染展厅录入列表
    renderShowroomList: function(entries) {
        // 更新分页信息
        this.showroomPagination.totalRecords = entries.length;
        this.showroomPagination.totalPages = Math.ceil(entries.length / this.showroomPagination.pageSize);

        if (entries.length === 0) {
            return '<div style="text-align: center; padding: 40px; color: var(--gray);">暂无展厅录入数据</div>';
        }

        // 计算当前页的数据
        const startIndex = (this.showroomPagination.currentPage - 1) * this.showroomPagination.pageSize;
        const endIndex = startIndex + this.showroomPagination.pageSize;
        const pageEntries = entries.slice(startIndex, endIndex);

        // 定义列配置
        const columns = [
            { key: 'entryDate', label: '录入日期', setting: 'entryDate' },
            { key: 'entryPersonnel', label: '录入人员', setting: 'entryPersonnel' },
            { key: 'salesAdvisor', label: '销售顾问', setting: 'salesAdvisor' },
            { key: 'stayDuration', label: '滞店时间', setting: 'stayDuration' },
            { key: 'customerName', label: '客户名称', setting: 'customerName' },
            { key: 'phone', label: '电话', setting: 'phone' },
            { key: 'visitType', label: '来店类型', setting: 'visitType' },
            { key: 'region', label: '区域', setting: 'region' },
            { key: 'intention', label: '意向', setting: 'intention' },
            { key: 'intendedModels', label: '意向车型', setting: 'intendedModels' },
            { key: 'testDrive', label: '试驾', setting: 'testDrive' },
            { key: 'finance', label: '金融', setting: 'finance' },
            { key: 'tradeIn', label: '置换', setting: 'tradeIn' },
            { key: 'quote', label: '报价', setting: 'quote' }
        ];

        // 过滤显示的列
        const visibleColumns = columns.filter(col => this.showroomColumnSettings[col.setting]);

        return `
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            ${visibleColumns.map(col => `<th>${col.label}</th>`).join('')}
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${pageEntries.map(entry => `
                            <tr>
                                ${visibleColumns.map(col => {
                                    let value = entry[col.key] || '-';
                                    // 特殊处理某些字段
                                    if (col.key === 'testDrive') {
                                        value = entry.testDrive && entry.testDrive !== '' ? entry.testDrive : '否';
                                    } else if (col.key === 'finance') {
                                        value = `<span class="status ${entry.finance === '是' ? 'status-success' : 'status-secondary'}">${entry.finance}</span>`;
                                    } else if (col.key === 'tradeIn') {
                                        value = `<span class="status ${entry.tradeIn === '是' ? 'status-warning' : 'status-secondary'}">${entry.tradeIn}</span>`;
                                    }
                                    return `<td>${value}</td>`;
                                }).join('')}
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="viewShowroomEntry(${entry.id})" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteShowroomEntry(${entry.id})" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            ${this.renderShowroomPagination()}
        `;
    },

    // 渲染线索录入列表
    renderLeadsList: function(entries) {
        // 更新分页信息
        this.leadsPagination.totalRecords = entries.length;
        this.leadsPagination.totalPages = Math.ceil(entries.length / this.leadsPagination.pageSize);

        if (entries.length === 0) {
            return '<div style="text-align: center; padding: 40px; color: var(--gray);">暂无线索录入数据</div>';
        }

        // 计算当前页的数据
        const startIndex = (this.leadsPagination.currentPage - 1) * this.leadsPagination.pageSize;
        const endIndex = startIndex + this.leadsPagination.pageSize;
        const pageEntries = entries.slice(startIndex, endIndex);

        // 定义列配置
        const columns = [
            { key: 'entryDate', label: '录入日期', setting: 'entryDate' },
            { key: 'isValid', label: '是否有效', setting: 'isValid' },
            { key: 'isDuplicate', label: '是否重复', setting: 'isDuplicate' },
            { key: 'customerName', label: '客户名称', setting: 'customerName' },
            { key: 'phone', label: '电话', setting: 'phone' },
            { key: 'leadId', label: '线索ID', setting: 'leadId' },
            { key: 'smartNumber', label: '智慧号', setting: 'smartNumber' },
            { key: 'intendedModels', label: '意向车型', setting: 'intendedModels' },
            { key: 'region', label: '区域', setting: 'region' },
            { key: 'wechat', label: '微信', setting: 'wechat' },
            { key: 'channel', label: '渠道', setting: 'channel' },
            { key: 'visitDate', label: '到店日期', setting: 'visitDate' },
            { key: 'dealDate', label: '成交日期', setting: 'dealDate' },
            { key: 'salesFollow', label: '转销售跟进', setting: 'salesFollow' },
            { key: 'receptionAdvisor', label: '接待顾问', setting: 'receptionAdvisor' },
            { key: 'firstFollowDate', label: '首次跟进日期', setting: 'firstFollowDate' },
            { key: 'followStatus', label: '跟进情况', setting: 'followStatus' }
        ];

        // 过滤显示的列
        const visibleColumns = columns.filter(col => this.leadsColumnSettings[col.setting]);

        return `
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            ${visibleColumns.map(col => `<th>${col.label}</th>`).join('')}
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${pageEntries.map(entry => `
                            <tr>
                                ${visibleColumns.map(col => {
                                    let value = entry[col.key] || '-';
                                    // 特殊处理某些字段
                                    if (col.key === 'isValid') {
                                        value = `<span class="status ${entry.isValid ? 'valid' : 'invalid'}">${entry.isValid ? '有效' : '无效'}</span>`;
                                    } else if (col.key === 'isDuplicate') {
                                        value = `<span class="status ${entry.isDuplicate ? 'status-warning' : 'status-success'}">${entry.isDuplicate ? '是' : '否'}</span>`;
                                    }
                                    return `<td>${value}</td>`;
                                }).join('')}
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="viewLeadEntry(${entry.id})" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteLeadEntry(${entry.id})" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            ${this.renderLeadsPagination()}
        `;
    },

    // 切换标签页
    switchTab: function(tabName) {
        this.currentTab = tabName;
        const contentContainer = document.getElementById('customer-content');
        if (contentContainer) {
            contentContainer.innerHTML = this.renderTabContent();
        }

        // 更新标签页样式
        document.querySelectorAll('.customer-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 重新绑定事件
        this.bindTabEvents();

        // 如果是客户列表标签页，渲染图表
        if (tabName === 'customers') {
            setTimeout(() => this.renderStatusChart(this.allCustomers), 100);
        }
    },

    // 绑定标签页事件
    bindTabEvents: function() {
        // 标签页切换事件
        document.querySelectorAll('.customer-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.currentTarget.dataset.tab;
                this.switchTab(tabName);
            });
        });

        // 根据当前标签页绑定相应事件
        switch(this.currentTab) {
            case 'customers':
                this.bindCustomerEvents();
                break;
            case 'showroom':
                this.bindShowroomEvents();
                break;
            case 'leads':
                this.bindLeadsEvents();
                break;
        }
    },

    // 绑定展厅录入事件
    bindShowroomEvents: function() {
        const newShowroomBtn = document.getElementById('new-showroom-btn');
        if (newShowroomBtn) {
            newShowroomBtn.addEventListener('click', () => this.showShowroomEntryForm());
        }

        const searchInput = document.getElementById('showroom-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const filteredEntries = this.allShowroomEntries.filter(entry =>
                    entry.customerName.toLowerCase().includes(searchTerm) ||
                    entry.phone.includes(searchTerm)
                );

                this.filteredShowroomEntries = filteredEntries;
                // 重置分页到第一页
                this.showroomPagination.currentPage = 1;

                const listContainer = document.getElementById('all-showroom-entries');
                if (listContainer) {
                    listContainer.innerHTML = this.renderShowroomList(filteredEntries);
                }
            });
        }
    },

    // 绑定线索录入事件
    bindLeadsEvents: function() {
        const newLeadBtn = document.getElementById('new-lead-btn');
        if (newLeadBtn) {
            newLeadBtn.addEventListener('click', () => this.showLeadEntryForm());
        }

        const searchInput = document.getElementById('leads-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const filteredEntries = this.allLeadEntries.filter(entry =>
                    entry.customerName.toLowerCase().includes(searchTerm) ||
                    entry.phone.includes(searchTerm)
                );

                this.filteredLeadEntries = filteredEntries;
                // 重置分页到第一页
                this.leadsPagination.currentPage = 1;

                const listContainer = document.getElementById('all-lead-entries');
                if (listContainer) {
                    listContainer.innerHTML = this.renderLeadsList(filteredEntries);
                }
            });
        }
    },

    // 显示展厅录入详情
    showShowroomDetails: async function(entryId) {
        try {
            const entry = await window.dbFunctions.getShowroomEntryById(entryId);
            if (!entry) {
                alert('展厅录入记录不存在');
                return;
            }

            this.showModal('展厅录入详情', this.renderShowroomDetails(entry));
        } catch (error) {
            console.error('显示展厅录入详情失败:', error);
            alert('显示展厅录入详情失败: ' + error.message);
        }
    },

    // 渲染展厅录入详情
    renderShowroomDetails: function(entry) {
        return `
            <div class="showroom-details" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="detail-group">
                    <label>录入日期：</label>
                    <span>${entry.entryDate || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>录入人员：</label>
                    <span>${entry.entryPersonnel || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>销售顾问：</label>
                    <span>${entry.salesAdvisor || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>来店时间：</label>
                    <span>${entry.arrivalTime || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>离店时间：</label>
                    <span>${entry.departureTime || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>滞店时间：</label>
                    <span>${entry.stayDuration || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>来店类型：</label>
                    <span>${entry.visitType || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>来店渠道：</label>
                    <span>${entry.channel || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>客户名称：</label>
                    <span>${entry.customerName || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>性别：</label>
                    <span>${entry.gender || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>电话：</label>
                    <span>${entry.phone || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>意向车型：</label>
                    <span>${entry.intendedModels || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>意向：</label>
                    <span>${entry.intention || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>区域：</label>
                    <span>${entry.region || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>现有车型：</label>
                    <span>${entry.currentModel || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>对比车型：</label>
                    <span>${entry.competitorModel || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>金融：</label>
                    <span class="status ${entry.finance === '是' ? 'status-success' : 'status-secondary'}">${entry.finance || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>置换：</label>
                    <span class="status ${entry.tradeIn === '是' ? 'status-warning' : 'status-secondary'}">${entry.tradeIn || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>试驾：</label>
                    <span>${entry.testDrive || '-'}</span>
                </div>
                <div class="detail-group" style="grid-column: 1 / -1;">
                    <label>备注：</label>
                    <span>${entry.notes || '-'}</span>
                </div>

                <div class="detail-actions" style="grid-column: 1 / -1; margin-top: 20px; text-align: center;">
                    <button type="button" class="btn btn-primary" onclick="editShowroomEntry(${entry.id})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()" style="margin-left: 10px;">
                        <i class="fas fa-times"></i> 关闭
                    </button>
                </div>
            </div>
        `;
    },

    // 显示线索录入详情
    showLeadDetails: async function(entryId) {
        try {
            const entry = await window.dbFunctions.getLeadEntryById(entryId);
            if (!entry) {
                alert('线索录入记录不存在');
                return;
            }

            this.showModal('线索录入详情', this.renderLeadDetails(entry));
        } catch (error) {
            console.error('显示线索录入详情失败:', error);
            alert('显示线索录入详情失败: ' + error.message);
        }
    },

    // 渲染线索录入详情
    renderLeadDetails: function(entry) {
        return `
            <div class="lead-details" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="detail-group">
                    <label>录入日期：</label>
                    <span>${entry.entryDate || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>是否有效：</label>
                    <span class="status ${entry.isValid ? 'status-success' : 'status-danger'}">${entry.isValid ? '有效' : '无效'}</span>
                </div>
                <div class="detail-group">
                    <label>客户名称：</label>
                    <span>${entry.customerName || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>性别：</label>
                    <span>${entry.gender || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>电话：</label>
                    <span>${entry.phone || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>线索ID：</label>
                    <span>${entry.leadId || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>意向车型：</label>
                    <span>${entry.intendedModels || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>渠道：</label>
                    <span>${entry.channel || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>接待顾问：</label>
                    <span>${entry.receptionAdvisor || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>跟进情况：</label>
                    <span>${entry.followStatus || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>线索来源：</label>
                    <span>${entry.leadSource || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>客户类型：</label>
                    <span>${entry.customerType || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>预算范围：</label>
                    <span>${entry.budgetRange || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>购车时间：</label>
                    <span>${entry.purchaseTime || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>区域：</label>
                    <span>${entry.region || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>竞品车型：</label>
                    <span>${entry.competitorModel || '-'}</span>
                </div>
                <div class="detail-group" style="grid-column: 1 / -1;">
                    <label>备注：</label>
                    <span>${entry.notes || '-'}</span>
                </div>

                <div class="detail-actions" style="grid-column: 1 / -1; margin-top: 20px; text-align: center;">
                    <button type="button" class="btn btn-primary" onclick="editLeadEntry(${entry.id})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()" style="margin-left: 10px;">
                        <i class="fas fa-times"></i> 关闭
                    </button>
                </div>
            </div>
        `;
    },

    // 显示模态框
    showModal: function(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    },

    // 渲染展厅录入表单
    renderShowroomEntryForm: function(entry, data, editId) {
        return `
            <form id="showroom-entry-form" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="form-group">
                    <label>录入日期 *</label>
                    <input type="date" name="entryDate" value="${entry.entryDate}" required>
                </div>
                <div class="form-group">
                    <label>录入人员 *</label>
                    <select name="entryPersonnel" required>
                        <option value="">请选择录入人员</option>
                        ${data.inputPersonnel.map(person => `<option value="${person.name}" ${entry.entryPersonnel === person.name ? 'selected' : ''}>${person.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>销售顾问 *</label>
                    <select name="salesAdvisor" required>
                        <option value="">请选择销售顾问</option>
                        ${data.salesAdvisors.map(advisor => `<option value="${advisor.name}" ${entry.salesAdvisor === advisor.name ? 'selected' : ''}>${advisor.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>来店时间 *</label>
                    <input type="time" name="arrivalTime" value="${entry.arrivalTime}" required onchange="customerFunctions.calculateStayDuration()">
                </div>
                <div class="form-group">
                    <label>离店时间 *</label>
                    <input type="time" name="departureTime" value="${entry.departureTime}" required onchange="customerFunctions.calculateStayDuration()">
                </div>
                <div class="form-group">
                    <label>滞店时间</label>
                    <input type="text" name="stayDuration" value="${entry.stayDuration}" readonly>
                </div>
                <div class="form-group">
                    <label>来店类型</label>
                    <select name="visitType">
                        <option value="">请选择来店类型</option>
                        ${data.visitTypes.map(type => `<option value="${type.name}" ${entry.visitType === type.name ? 'selected' : ''}>${type.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>来店渠道</label>
                    <select name="channel">
                        <option value="">请选择来店渠道</option>
                        ${data.channels.map(channel => `<option value="${channel.name}" ${entry.channel === channel.name ? 'selected' : ''}>${channel.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>客户名称 *</label>
                    <input type="text" name="customerName" value="${entry.customerName}" required>
                </div>
                <div class="form-group">
                    <label>性别</label>
                    <select name="gender">
                        <option value="男" ${entry.gender === '男' ? 'selected' : ''}>男</option>
                        <option value="女" ${entry.gender === '女' ? 'selected' : ''}>女</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>电话 *</label>
                    <input type="text" name="phone" value="${entry.phone}" pattern="[0-9]{11}|无效" required placeholder="11位手机号或'无效'">
                </div>
                <div class="form-group">
                    <label>意向车型</label>
                    <select name="intendedModels" multiple style="height: 100px;">
                        ${data.carModels.map(model => `<option value="${model.name}" ${entry.intendedModels && entry.intendedModels.includes(model.name) ? 'selected' : ''}>${model.name}</option>`).join('')}
                    </select>
                    <small style="color: #6c757d;">按住Ctrl键可多选</small>
                </div>
                <div class="form-group">
                    <label>意向</label>
                    <select name="intention">
                        <option value="">请选择意向</option>
                        ${data.intentions.map(intention => `<option value="${intention.name}" ${entry.intention === intention.name ? 'selected' : ''}>${intention.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>区域</label>
                    <select name="region">
                        <option value="">请选择区域</option>
                        ${data.regions.map(region => `<option value="${region.name}" ${entry.region === region.name ? 'selected' : ''}>${region.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>现有车型</label>
                    <input type="text" name="currentModel" value="${entry.currentModel}">
                </div>
                <div class="form-group">
                    <label>对比车型</label>
                    <select name="competitorModel" onchange="customerFunctions.handleCompetitorChange(this)">
                        <option value="">请选择对比车型</option>
                        ${data.competitors.map(comp => `<option value="${comp.name}" ${entry.competitorModel === comp.name ? 'selected' : ''}>${comp.name}</option>`).join('')}
                        <option value="custom">自定义输入...</option>
                    </select>
                    <input type="text" name="competitorModelCustom" value="${entry.competitorModel && !data.competitors.find(c => c.name === entry.competitorModel) ? entry.competitorModel : ''}" placeholder="请输入对比车型" style="margin-top: 5px; display: ${entry.competitorModel && !data.competitors.find(c => c.name === entry.competitorModel) ? 'block' : 'none'};">
                </div>
                <div class="form-group">
                    <label>金融</label>
                    <select name="finance">
                        <option value="是" ${entry.finance === '是' ? 'selected' : ''}>是</option>
                        <option value="否" ${entry.finance === '否' ? 'selected' : ''}>否</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>置换</label>
                    <select name="tradeIn">
                        <option value="是" ${entry.tradeIn === '是' ? 'selected' : ''}>是</option>
                        <option value="否" ${entry.tradeIn === '否' ? 'selected' : ''}>否</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>是否试驾</label>
                    <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="radio" name="hasTestDrive" value="是" ${entry.testDrive && entry.testDrive !== '' ? 'checked' : ''} onchange="customerFunctions.toggleTestDriveModels(this)">
                            是
                        </label>
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="radio" name="hasTestDrive" value="否" ${!entry.testDrive || entry.testDrive === '' ? 'checked' : ''} onchange="customerFunctions.toggleTestDriveModels(this)">
                            否
                        </label>
                    </div>
                    <div id="testDriveModels" style="display: ${entry.testDrive && entry.testDrive !== '' ? 'block' : 'none'};">
                        <label>试驾车型</label>
                        <select name="testDrive" multiple style="height: 80px;">
                            ${data.testDriveModels.map(model => `<option value="${model.name}" ${entry.testDrive && entry.testDrive.includes(model.name) ? 'selected' : ''}>${model.name}</option>`).join('')}
                        </select>
                        <small style="color: #6c757d;">按住Ctrl键可多选</small>
                    </div>
                </div>
                <div class="form-group">
                    <label>是否报价</label>
                    <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="radio" name="hasQuote" value="是" ${entry.quoteAmount && entry.quoteAmount !== '' ? 'checked' : ''} onchange="customerFunctions.toggleQuoteAmount(this)">
                            是
                        </label>
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="radio" name="hasQuote" value="否" ${!entry.quoteAmount || entry.quoteAmount === '' ? 'checked' : ''} onchange="customerFunctions.toggleQuoteAmount(this)">
                            否
                        </label>
                    </div>
                    <div id="quoteAmount" style="display: ${entry.quoteAmount && entry.quoteAmount !== '' ? 'block' : 'none'};">
                        <label>报价金额（万元）</label>
                        <input type="number" name="quoteAmount" value="${entry.quoteAmount || ''}" placeholder="请输入报价金额" step="0.01" min="0">
                        <small style="color: #6c757d;">请输入数字，单位：万元</small>
                    </div>
                </div>
                <div class="form-group" style="grid-column: 1 / -1;">
                    <label>备注</label>
                    <textarea name="notes" rows="3">${entry.notes}</textarea>
                </div>
                <div class="form-actions" style="grid-column: 1 / -1; margin-top: 20px;">
                    <button type="button" class="btn btn-primary" onclick="customerFunctions.saveShowroomEntry(${editId})">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </form>
        `;
    },

    // 渲染线索录入表单
    renderLeadEntryForm: function(entry, data, editId) {
        return `
            <form id="lead-entry-form" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="form-group">
                    <label>录入日期 *</label>
                    <input type="date" name="entryDate" value="${entry.entryDate}" required>
                </div>
                <div class="form-group">
                    <label>是否有效 *</label>
                    <select name="isValid" required>
                        <option value="true" ${entry.isValid === true || entry.isValid === 'true' ? 'selected' : ''}>是</option>
                        <option value="false" ${entry.isValid === false || entry.isValid === 'false' ? 'selected' : ''}>否</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>是否重复</label>
                    <select name="isDuplicate">
                        <option value="false" ${entry.isDuplicate === false || entry.isDuplicate === 'false' || !entry.isDuplicate ? 'selected' : ''}>否</option>
                        <option value="true" ${entry.isDuplicate === true || entry.isDuplicate === 'true' ? 'selected' : ''}>是</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>客户名称 *</label>
                    <input type="text" name="customerName" value="${entry.customerName}" required>
                </div>
                <div class="form-group">
                    <label>电话 *</label>
                    <input type="text" name="phone" value="${entry.phone}" pattern="[0-9]{11}" required placeholder="11位手机号">
                </div>
                <div class="form-group">
                    <label>线索ID</label>
                    <input type="text" name="leadId" value="${entry.leadId}">
                </div>
                <div class="form-group">
                    <label>智慧号</label>
                    <input type="text" name="smartNumber" value="${entry.smartNumber}">
                </div>
                <div class="form-group">
                    <label>意向车型</label>
                    <select name="intendedModels" multiple>
                        ${data.carModels.map(model => `<option value="${model.name}" ${entry.intendedModels && entry.intendedModels.includes(model.name) ? 'selected' : ''}>${model.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>区域</label>
                    <select name="region">
                        <option value="">请选择区域</option>
                        ${data.regions.map(region => `<option value="${region.name}" ${entry.region === region.name ? 'selected' : ''}>${region.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>微信</label>
                    <select name="wechat">
                        <option value="是" ${entry.wechat === '是' ? 'selected' : ''}>是</option>
                        <option value="否" ${entry.wechat === '否' ? 'selected' : ''}>否</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>渠道</label>
                    <select name="channel" onchange="customerFunctions.handleChannelChange(this)">
                        <option value="">请选择渠道</option>
                        ${data.leadChannels.map(channel => `<option value="${channel.name}" ${entry.channel === channel.name ? 'selected' : ''}>${channel.name}</option>`).join('')}
                        <option value="custom">自定义输入...</option>
                    </select>
                    <input type="text" name="channelCustom" value="${entry.channel && !data.leadChannels.find(c => c.name === entry.channel) ? entry.channel : ''}" placeholder="请输入渠道名称" style="margin-top: 5px; display: ${entry.channel && !data.leadChannels.find(c => c.name === entry.channel) ? 'block' : 'none'};">
                </div>
                <div class="form-group">
                    <label>到店日期</label>
                    <input type="date" name="visitDate" value="${entry.visitDate}">
                </div>
                <div class="form-group">
                    <label>成交日期</label>
                    <input type="date" name="dealDate" value="${entry.dealDate}">
                </div>
                <div class="form-group">
                    <label>转销售跟进</label>
                    <select name="salesFollow">
                        <option value="">请选择销售顾问</option>
                        ${data.salesAdvisors.map(advisor => `<option value="${advisor.name}" ${entry.salesFollow === advisor.name ? 'selected' : ''}>${advisor.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>接待顾问</label>
                    <select name="receptionAdvisor">
                        <option value="">请选择接待顾问</option>
                        ${data.salesAdvisors.map(advisor => `<option value="${advisor.name}" ${entry.receptionAdvisor === advisor.name ? 'selected' : ''}>${advisor.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>首次跟进日期</label>
                    <input type="date" name="firstFollowDate" value="${entry.firstFollowDate}">
                </div>
                <div class="form-group" style="grid-column: 1 / -1;">
                    <label>跟进情况</label>
                    <textarea name="followStatus" rows="3">${entry.followStatus || ''}</textarea>
                </div>
                <div class="form-actions" style="grid-column: 1 / -1; margin-top: 20px;">
                    <button type="button" class="btn btn-primary" onclick="customerFunctions.saveLeadEntry(${editId})">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </form>
        `;
    },

    // 计算滞店时间
    calculateStayDuration: function() {
        const arrivalTime = document.querySelector('input[name="arrivalTime"]').value;
        const departureTime = document.querySelector('input[name="departureTime"]').value;

        if (arrivalTime && departureTime) {
            const arrival = new Date(`2000-01-01 ${arrivalTime}`);
            const departure = new Date(`2000-01-01 ${departureTime}`);

            if (departure > arrival) {
                const diffMs = departure - arrival;
                const hours = Math.floor(diffMs / (1000 * 60 * 60));
                const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

                const stayDuration = `${hours}小时${minutes}分钟`;
                document.querySelector('input[name="stayDuration"]').value = stayDuration;
            }
        }
    },

    // 保存展厅录入
    saveShowroomEntry: async function(editId) {
        try {
            const form = document.getElementById('showroom-entry-form');
            const formData = new FormData(form);
            const entryData = {};

            for (let [key, value] of formData.entries()) {
                entryData[key] = value;
            }

            // 处理多选字段
            const intendedModelsSelect = form.querySelector('select[name="intendedModels"]');
            const selectedModels = Array.from(intendedModelsSelect.selectedOptions).map(option => option.value);
            entryData.intendedModels = selectedModels.join(',');

            // 处理试驾字段
            const hasTestDrive = form.querySelector('input[name="hasTestDrive"]:checked').value;
            if (hasTestDrive === '是') {
                const testDriveSelect = form.querySelector('select[name="testDrive"]');
                const selectedTestDrive = Array.from(testDriveSelect.selectedOptions).map(option => option.value);
                entryData.testDrive = selectedTestDrive.join(',');
            } else {
                entryData.testDrive = '';
            }

            // 处理报价字段
            const hasQuote = form.querySelector('input[name="hasQuote"]:checked').value;
            if (hasQuote === '是') {
                const quoteAmountInput = form.querySelector('input[name="quoteAmount"]');
                entryData.quote = '是';
                entryData.quoteAmount = quoteAmountInput.value || '';
            } else {
                entryData.quote = '否';
                entryData.quoteAmount = '';
            }

            // 处理对比车型字段
            const competitorSelect = form.querySelector('select[name="competitorModel"]');
            const customInput = form.querySelector('input[name="competitorModelCustom"]');
            if (competitorSelect.value === 'custom') {
                entryData.competitorModel = customInput.value;
            } else {
                entryData.competitorModel = competitorSelect.value;
            }

            // 验证必填字段
            if (!entryData.entryDate || !entryData.entryPersonnel || !entryData.salesAdvisor ||
                !entryData.arrivalTime || !entryData.departureTime || !entryData.customerName || !entryData.phone) {
                alert('请填写必填字段');
                return;
            }

            // 验证手机号格式
            const phoneRegex = /^([0-9]{11}|无效)$/;
            if (!phoneRegex.test(entryData.phone)) {
                alert('电话格式不正确，请输入11位手机号或"无效"');
                return;
            }

            if (editId !== null) {
                await window.dbFunctions.updateShowroomEntry(editId, entryData);
                alert('展厅录入更新成功');
            } else {
                await window.dbFunctions.addShowroomEntry(entryData);
                alert('展厅录入添加成功');
            }

            // 关闭模态框
            document.querySelector('.modal-overlay').remove();

            // 重新加载数据
            this.allShowroomEntries = await window.dbFunctions.getAllShowroomEntries();
            this.switchTab('showroom');

        } catch (error) {
            console.error('保存展厅录入失败:', error);
            alert('保存展厅录入失败: ' + error.message);
        }
    },

    // 保存线索录入
    saveLeadEntry: async function(editId) {
        try {
            const form = document.getElementById('lead-entry-form');
            const formData = new FormData(form);
            const entryData = {};

            for (let [key, value] of formData.entries()) {
                entryData[key] = value;
            }

            // 处理多选字段
            const intendedModelsSelect = form.querySelector('select[name="intendedModels"]');
            const selectedModels = Array.from(intendedModelsSelect.selectedOptions).map(option => option.value);
            entryData.intendedModels = selectedModels.join(',');

            // 处理渠道字段
            const channelSelect = form.querySelector('select[name="channel"]');
            const channelCustomInput = form.querySelector('input[name="channelCustom"]');
            if (channelSelect.value === 'custom') {
                entryData.channel = channelCustomInput.value;
            } else {
                entryData.channel = channelSelect.value;
            }

            // 处理布尔值字段
            entryData.isValid = entryData.isValid === 'true';
            entryData.isDuplicate = entryData.isDuplicate === 'true';

            // 验证必填字段
            if (!entryData.entryDate || !entryData.customerName || !entryData.phone) {
                alert('请填写必填字段');
                return;
            }

            // 验证手机号格式
            const phoneRegex = /^[0-9]{11}$/;
            if (!phoneRegex.test(entryData.phone)) {
                alert('电话格式不正确，请输入11位手机号');
                return;
            }

            if (editId !== null) {
                await window.dbFunctions.updateLeadEntry(editId, entryData);
                alert('线索录入更新成功');
            } else {
                await window.dbFunctions.addLeadEntry(entryData);
                alert('线索录入添加成功');
            }

            // 关闭模态框
            document.querySelector('.modal-overlay').remove();

            // 重新加载数据
            this.allLeadEntries = await window.dbFunctions.getAllLeadEntries();
            this.switchTab('leads');

        } catch (error) {
            console.error('保存线索录入失败:', error);
            alert('保存线索录入失败: ' + error.message);
        }
    },

    // 处理对比车型选择变化
    handleCompetitorChange: function(select) {
        const customInput = select.parentNode.querySelector('input[name="competitorModelCustom"]');
        if (select.value === 'custom') {
            customInput.style.display = 'block';
            customInput.focus();
        } else {
            customInput.style.display = 'none';
            customInput.value = '';
        }
    },

    // 处理渠道选择变化
    handleChannelChange: function(select) {
        const customInput = select.parentNode.querySelector('input[name="channelCustom"]');
        if (select.value === 'custom') {
            customInput.style.display = 'block';
            customInput.focus();
        } else {
            customInput.style.display = 'none';
            customInput.value = '';
        }
    },

    // 切换试驾车型显示
    toggleTestDriveModels: function(radio) {
        const testDriveModels = document.getElementById('testDriveModels');
        const testDriveSelect = testDriveModels.querySelector('select[name="testDrive"]');

        if (radio.value === '是') {
            testDriveModels.style.display = 'block';
        } else {
            testDriveModels.style.display = 'none';
            // 清空选择
            Array.from(testDriveSelect.options).forEach(option => option.selected = false);
        }
    },

    // 切换报价金额显示
    toggleQuoteAmount: function(radio) {
        const quoteAmount = document.getElementById('quoteAmount');
        const quoteInput = quoteAmount.querySelector('input[name="quoteAmount"]');

        if (radio.value === '是') {
            quoteAmount.style.display = 'block';
            quoteInput.focus();
        } else {
            quoteAmount.style.display = 'none';
            // 清空输入
            quoteInput.value = '';
        }
    },

    // 展厅录入日期筛选
    filterShowroomByDate: function() {
        const startDate = document.getElementById('showroom-start-date').value;
        const endDate = document.getElementById('showroom-end-date').value;

        if (!startDate && !endDate) {
            this.filteredShowroomEntries = this.allShowroomEntries;
        } else {
            this.filteredShowroomEntries = this.allShowroomEntries.filter(entry => {
                const entryDate = entry.entryDate;
                if (startDate && endDate) {
                    return entryDate >= startDate && entryDate <= endDate;
                } else if (startDate) {
                    return entryDate >= startDate;
                } else if (endDate) {
                    return entryDate <= endDate;
                }
                return true;
            });
        }

        // 重置分页到第一页
        this.showroomPagination.currentPage = 1;

        // 更新显示
        document.getElementById('all-showroom-entries').innerHTML = this.renderShowroomList(this.filteredShowroomEntries);
    },

    // 重置展厅录入筛选
    resetShowroomFilter: function() {
        document.getElementById('showroom-start-date').value = '';
        document.getElementById('showroom-end-date').value = '';
        this.filteredShowroomEntries = this.allShowroomEntries;
        // 重置分页到第一页
        this.showroomPagination.currentPage = 1;
        document.getElementById('all-showroom-entries').innerHTML = this.renderShowroomList(this.allShowroomEntries);
    },

    // 导出展厅录入数据 - 统一化导出功能
    exportShowroomToExcel: function() {
        try {
            const data = this.filteredShowroomEntries || this.allShowroomEntries;

            if (!data || data.length === 0) {
                alert('没有可导出的展厅录入数据');
                return;
            }

            // 显示导出选项模态框
            const modalContent = `
                <div class="export-modal">
                    <h3><i class="fas fa-download"></i> 导出展厅录入数据</h3>
                    <div style="margin: 20px 0;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出格式：</label>
                            <label style="margin-right: 20px;">
                                <input type="radio" name="export-format" value="csv" checked> CSV格式
                            </label>
                            <label>
                                <input type="radio" name="export-format" value="excel"> Excel格式
                            </label>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出字段（按要求顺序排列）：</label>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                <label><input type="checkbox" value="entryDate" checked> 录入日期</label>
                                <label><input type="checkbox" value="entryPersonnel" checked> 录入人员</label>
                                <label><input type="checkbox" value="salesAdvisor" checked> 销售顾问</label>
                                <label><input type="checkbox" value="arrivalTime" checked> 来店时间</label>
                                <label><input type="checkbox" value="departureTime" checked> 离店时间</label>
                                <label><input type="checkbox" value="stayDuration" checked> 滞店时间</label>
                                <label><input type="checkbox" value="visitType" checked> 来店类型</label>
                                <label><input type="checkbox" value="channel" checked> 来店渠道</label>
                                <label><input type="checkbox" value="customerName" checked> 客户名称</label>
                                <label><input type="checkbox" value="gender" checked> 性别</label>
                                <label><input type="checkbox" value="phone" checked> 电话</label>
                                <label><input type="checkbox" value="intendedModels" checked> 意向车型</label>
                                <label><input type="checkbox" value="intention" checked> 意向</label>
                                <label><input type="checkbox" value="region" checked> 区域</label>
                                <label><input type="checkbox" value="currentModel" checked> 现有车型</label>
                                <label><input type="checkbox" value="competitorModel" checked> 对比车型</label>
                                <label><input type="checkbox" value="finance" checked> 金融</label>
                                <label><input type="checkbox" value="tradeIn" checked> 置换</label>
                                <label><input type="checkbox" value="testDrive" checked> 试驾</label>
                                <label><input type="checkbox" value="notes" checked> 备注</label>
                            </div>
                            <div style="margin-top: 10px;">
                                <button type="button" class="btn btn-sm btn-outline" onclick="customerFunctions.selectAllShowroomFields(true)">全选</button>
                                <button type="button" class="btn btn-sm btn-outline" onclick="customerFunctions.selectAllShowroomFields(false)">全不选</button>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button class="btn btn-primary" id="confirm-export-btn">确认导出</button>
                    </div>
                </div>
            `;

            this.showModal('导出数据', modalContent);

            // 绑定导出确认事件
            document.getElementById('confirm-export-btn').addEventListener('click', () => {
                const format = document.querySelector('input[name="export-format"]:checked').value;
                const selectedFields = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                    .map(cb => cb.value);

                if (selectedFields.length === 0) {
                    alert('请至少选择一个字段');
                    return;
                }

                this.performShowroomExport(format, selectedFields, data);
                const modal = document.querySelector('.modal-overlay');
                if (modal) modal.remove();
            });

        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败: ' + error.message);
        }
    },

    // 全选/全不选展厅录入导出字段
    selectAllShowroomFields: function(selectAll) {
        const checkboxes = document.querySelectorAll('input[type="checkbox"][value]');
        checkboxes.forEach(cb => {
            cb.checked = selectAll;
        });
    },

    // 执行展厅录入导出
    performShowroomExport: function(format, selectedFields, data) {
        try {
            // 字段名映射（按要求顺序）
            const fieldNames = {
                entryDate: '录入日期',
                entryPersonnel: '录入人员',
                salesAdvisor: '销售顾问',
                arrivalTime: '来店时间',
                departureTime: '离店时间',
                stayDuration: '滞店时间',
                visitType: '来店类型',
                channel: '来店渠道',
                customerName: '客户名称',
                gender: '性别',
                phone: '电话',
                intendedModels: '意向车型',
                intention: '意向',
                region: '区域',
                currentModel: '现有车型',
                competitorModel: '对比车型',
                finance: '金融',
                tradeIn: '置换',
                testDrive: '试驾',
                notes: '备注'
            };

            // 定义字段顺序（按要求排列）
            const fieldOrder = [
                'entryDate', 'entryPersonnel', 'salesAdvisor', 'arrivalTime', 'departureTime', 'stayDuration',
                'visitType', 'channel', 'customerName', 'gender', 'phone',
                'intendedModels', 'intention', 'region', 'currentModel', 'competitorModel',
                'finance', 'tradeIn', 'testDrive', 'notes'
            ];

            // 按顺序过滤选中的字段
            const orderedSelectedFields = fieldOrder.filter(field => selectedFields.includes(field));

            // 准备导出数据
            const exportData = data.map(item => {
                const row = {};
                orderedSelectedFields.forEach(field => {
                    let value = item[field] || '';
                    // 处理特殊字段格式
                    if (field === 'finance' || field === 'tradeIn') {
                        value = value === true || value === '是' ? '是' : '否';
                    }
                    row[fieldNames[field]] = value;
                });
                return row;
            });

            if (format === 'csv') {
                this.exportShowroomToCSV(exportData);
            } else {
                this.exportShowroomToExcel_New(exportData);
            }

        } catch (error) {
            console.error('导出处理失败:', error);
            alert('导出处理失败: ' + error.message);
        }
    },

    // 导出展厅录入到CSV
    exportShowroomToCSV: function(data) {
        if (data.length === 0) return;

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');

        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `展厅录入数据_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('CSV文件导出成功！');
    },

    // 导出展厅录入到Excel（新版本）
    exportShowroomToExcel_New: function(data) {
        if (data.length === 0) return;

        try {
            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'ShowroomEntries');

            // 设置列宽
            const colWidths = Object.keys(data[0]).map(() => ({ wch: 15 }));
            worksheet['!cols'] = colWidths;

            XLSX.writeFile(workbook, `展厅录入数据_${new Date().toISOString().split('T')[0]}.xlsx`);
            alert('Excel文件导出成功！');

        } catch (error) {
            console.error('Excel导出失败:', error);
            alert('Excel导出失败，请确保已加载XLSX库: ' + error.message);
        }
    },

    // 导入展厅录入Excel
    importShowroomFromExcel: function() {
        const modalContent = `
            <div class="import-modal">
                <h3><i class="fas fa-upload"></i> 导入展厅录入数据</h3>
                <div style="margin: 20px 0;">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导入格式：</label>
                        <label style="margin-right: 20px;">
                            <input type="radio" name="import-format" value="excel" checked> Excel格式 (.xlsx, .xls)
                        </label>
                        <label>
                            <input type="radio" name="import-format" value="csv"> CSV格式 (.csv)
                        </label>
                    </div>
                    <p style="color: #6c757d; margin-bottom: 15px;">请确保文件包含以下字段：</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                        <strong>必需字段：</strong> 客户名称, 电话<br>
                        <strong>可选字段：</strong> 录入日期, 录入人员, 销售顾问, 到店时间, 离店时间, 来店类型, 渠道, 意向车型等
                    </div>
                    <input type="file" id="showroom-file-input" accept=".xlsx,.xls,.csv" style="margin-bottom: 15px; width: 100%;">
                    <div id="showroom-import-progress" style="display: none; margin-bottom: 15px;">
                        <div style="background: #e9ecef; border-radius: 10px; overflow: hidden;">
                            <div id="showroom-import-progress-bar" style="background: #4361ee; height: 20px; width: 0%; transition: width 0.3s;"></div>
                        </div>
                        <div id="showroom-import-status" style="margin-top: 5px; font-size: 14px;"></div>
                    </div>
                </div>
                <div style="text-align: right;">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" id="confirm-showroom-import-btn" disabled>确认导入</button>
                </div>
            </div>
        `;

        this.showModal(modalContent);

        // 绑定格式选择事件
        document.querySelectorAll('input[name="import-format"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                const fileInput = document.getElementById('showroom-file-input');
                if (e.target.value === 'excel') {
                    fileInput.accept = '.xlsx,.xls';
                } else {
                    fileInput.accept = '.csv';
                }
                // 清空已选择的文件
                fileInput.value = '';
                document.getElementById('confirm-showroom-import-btn').disabled = true;
            });
        });

        // 绑定文件选择事件
        document.getElementById('showroom-file-input').addEventListener('change', (e) => {
            const file = e.target.files[0];
            const confirmBtn = document.getElementById('confirm-showroom-import-btn');

            if (file) {
                confirmBtn.disabled = false;
                confirmBtn.onclick = () => this.processShowroomImport(file);
            } else {
                confirmBtn.disabled = true;
            }
        });
    },

    // 处理展厅录入导入
    processShowroomImport: function(file) {
        const format = document.querySelector('input[name="import-format"]:checked').value;

        if (format === 'csv') {
            this.importFromCSV(file, 'showroomEntries', (data) => {
                // 验证必填字段 - 支持多种字段名称
                const customerName = data.customerName || data['客户名称'] || data['姓名'] || data['客户姓名'];
                const phone = data.phone || data['电话'] || data['手机'] || data['联系电话'] || data['手机号'];

                if (!customerName || !phone) {
                    console.error('字段验证失败:', { customerName, phone, originalData: data });
                    throw new Error('客户名称和电话为必填字段');
                }
                return true;
            });
        } else {
            this.importFromExcel(file, 'showroomEntries', (data) => {
                // 验证必填字段 - 支持多种字段名称
                const customerName = data.customerName || data['客户名称'] || data['姓名'] || data['客户姓名'];
                const phone = data.phone || data['电话'] || data['手机'] || data['联系电话'] || data['手机号'];

                if (!customerName || !phone) {
                    console.error('字段验证失败:', { customerName, phone, originalData: data });
                    throw new Error('客户名称和电话为必填字段');
                }
                return true;
            });
        }
    },

    // 处理展厅录入文件导入
    handleShowroomImport: function(fileInput) {
        const file = fileInput.files[0];
        if (!file) return;

        this.processShowroomImport(file);
    },

    // 处理线索录入文件导入
    handleLeadsImport: function(fileInput) {
        const file = fileInput.files[0];
        if (!file) return;

        this.processLeadsImport(file);
    },

    // 通用Excel导出函数
    exportToExcel: function(data, filename) {
        if (!window.XLSX) {
            alert('Excel功能未加载，请刷新页面重试');
            return;
        }

        if (!data || data.length === 0) {
            alert('没有数据可以导出');
            return;
        }

        try {
            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');

            // 设置列宽
            const colWidths = Object.keys(data[0]).map(() => ({ wch: 15 }));
            worksheet['!cols'] = colWidths;

            XLSX.writeFile(workbook, filename);
            alert('Excel文件导出成功！');

        } catch (error) {
            console.error('Excel导出失败:', error);
            alert('Excel导出失败，请确保已加载XLSX库: ' + error.message);
        }
    },

    // 通用Excel导入函数
    importFromExcel: async function(file, tableName, validateFunction) {
        if (!window.XLSX) {
            alert('Excel功能未加载，请刷新页面重试');
            return;
        }

        try {
            const data = await this.readExcelFile(file);
            let successCount = 0;
            let errorCount = 0;

            for (const row of data) {
                try {
                    if (validateFunction) {
                        validateFunction(row);
                    }

                    // 字段映射
                    const mappedRow = this.mapImportFields(row, tableName);

                    if (tableName === 'showroomEntries') {
                        await window.dbFunctions.addShowroomEntry(mappedRow);
                    } else if (tableName === 'leadEntries') {
                        await window.dbFunctions.addLeadEntry(mappedRow);
                    }
                    successCount++;
                } catch (error) {
                    console.error('导入行失败:', error, row);
                    errorCount++;
                }
            }

            alert(`导入完成！成功：${successCount}条，失败：${errorCount}条`);

            // 重新加载数据
            await this.loadCustomers();

        } catch (error) {
            console.error('导入失败:', error);
            alert('导入失败: ' + error.message);
        }
    },

    // 读取Excel文件
    readExcelFile: function(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, {type: 'array'});
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];

                    // 使用defval选项处理空值，并确保数据类型正确
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
                        defval: '', // 空值默认为空字符串
                        raw: false  // 不使用原始值，确保所有值都转换为字符串
                    });

                    // 进一步处理数据，确保所有字段都是字符串类型
                    const processedData = jsonData.map(row => {
                        const processedRow = {};
                        for (const [key, value] of Object.entries(row)) {
                            // 确保所有值都是字符串类型，避免split()方法错误
                            if (value === null || value === undefined) {
                                processedRow[key] = '';
                            } else if (typeof value === 'string') {
                                processedRow[key] = value.trim();
                            } else {
                                processedRow[key] = String(value).trim();
                            }
                        }
                        return processedRow;
                    });

                    resolve(processedData);
                } catch (error) {
                    console.error('Excel文件读取失败:', error);
                    reject(new Error('Excel文件读取失败: ' + error.message));
                }
            };
            reader.onerror = function(error) {
                console.error('文件读取错误:', error);
                reject(new Error('文件读取失败'));
            };
            reader.readAsArrayBuffer(file);
        });
    },

    // 通用CSV导入函数
    importFromCSV: async function(file, tableName, validateFunction) {
        try {
            const data = await this.readCSVFile(file);
            let successCount = 0;
            let errorCount = 0;

            for (const row of data) {
                try {
                    if (validateFunction) {
                        validateFunction(row);
                    }

                    // 字段映射
                    const mappedRow = this.mapImportFields(row, tableName);

                    if (tableName === 'showroomEntries') {
                        await window.dbFunctions.addShowroomEntry(mappedRow);
                    } else if (tableName === 'leadEntries') {
                        await window.dbFunctions.addLeadEntry(mappedRow);
                    }
                    successCount++;
                } catch (error) {
                    console.error('导入行失败:', error, row);
                    errorCount++;
                }
            }

            alert(`导入完成！成功：${successCount}条，失败：${errorCount}条`);

            // 重新加载数据
            await this.loadCustomers();

        } catch (error) {
            console.error('CSV导入失败:', error);
            alert('CSV导入失败: ' + error.message);
        }
    },

    // 读取CSV文件
    readCSVFile: function(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const text = e.target.result;
                    const lines = text.split('\n').filter(line => line.trim() !== '');

                    if (lines.length < 2) {
                        throw new Error('CSV文件格式不正确，至少需要标题行和一行数据');
                    }

                    // 解析标题行
                    const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''));

                    // 解析数据行
                    const data = [];
                    for (let i = 1; i < lines.length; i++) {
                        const values = lines[i].split(',').map(value => value.trim().replace(/"/g, ''));
                        const row = {};

                        headers.forEach((header, index) => {
                            row[header] = values[index] || '';
                        });

                        data.push(row);
                    }

                    resolve(data);
                } catch (error) {
                    console.error('CSV文件解析失败:', error);
                    reject(new Error('CSV文件解析失败: ' + error.message));
                }
            };
            reader.onerror = function(error) {
                console.error('文件读取错误:', error);
                reject(new Error('文件读取失败'));
            };
            reader.readAsText(file, 'UTF-8');
        });
    },

    // 字段映射函数
    mapImportFields: function(row, tableName) {
        const mappedRow = {};

        // 通用字段映射
        const fieldMapping = {
            // 客户名称字段
            'customerName': row.customerName || row['客户名称'] || row['姓名'] || row['客户姓名'] || '',
            // 电话字段
            'phone': row.phone || row['电话'] || row['手机'] || row['联系电话'] || row['手机号'] || '',
            // 录入日期字段
            'entryDate': row.entryDate || row['录入日期'] || row['日期'] || new Date().toISOString().split('T')[0],
            // 录入人员字段
            'entryPersonnel': row.entryPersonnel || row['录入人员'] || row['录入人'] || '',
            // 销售顾问字段
            'salesAdvisor': row.salesAdvisor || row['销售顾问'] || row['顾问'] || '',
            // 微信字段
            'wechat': row.wechat || row['微信'] || row['微信号'] || '',
            // 区域字段
            'region': row.region || row['区域'] || '',
            // 渠道字段
            'channel': row.channel || row['渠道'] || row['来源渠道'] || ''
        };

        // 根据表名添加特定字段映射
        if (tableName === 'showroomEntries') {
            // 展厅录入特有字段
            Object.assign(fieldMapping, {
                // 来店相关字段
                'visitType': row.visitType || row['来店类型'] || '',
                'arrivalTime': row.arrivalTime || row['来店时间'] || row['到店时间'] || '',
                'departureTime': row.departureTime || row['离店时间'] || '',
                'stayDuration': row.stayDuration || row['滞店时间'] || row['停留时间'] || '',

                // 客户信息字段
                'gender': row.gender || row['性别'] || '',

                // 意向相关字段
                'intention': row.intention || row['意向'] || row['购买意向'] || '',
                'intendedModels': row.intendedModels || row['意向车型'] || '',
                'currentModel': row.currentModel || row['现有车型'] || '',
                'competitorModel': row.competitorModel || row['对比车型'] || row['竞品'] || '',
                'testDrive': row.testDrive || row['试驾'] || row['试驾车型'] || '',

                // 金融相关字段
                'finance': row.finance || row['金融'] || row['贷款'] || row['金融方案'] || '',
                'tradeIn': row.tradeIn || row['置换'] || row['旧车置换'] || '',
                'quote': row.quote || row['报价'] || row['价格'] || '',
                'quoteAmount': row.quoteAmount || row['报价金额'] || '',

                // 备注字段
                'notes': row.notes || row['备注'] || row['备注信息'] || ''
            });
        } else if (tableName === 'leadEntries') {
            // 线索录入特有字段
            Object.assign(fieldMapping, {
                // 基本信息字段
                'isValid': row.isValid || row['是否有效'] || '',
                'isDuplicate': row.isDuplicate || row['是否重复'] || '',
                'leadId': row.leadId || row['线索ID'] || '',
                'smartNumber': row.smartNumber || row['智慧号'] || '',

                // 意向和区域字段
                'intendedModels': row.intendedModels || row['意向车型'] || '',

                // 联系方式字段
                'wechat': row.wechat || row['微信'] || row['微信号'] || '',

                // 日期相关字段
                'visitDate': row.visitDate || row['到店日期'] || row['来店日期'] || '',
                'dealDate': row.dealDate || row['成交日期'] || row['签约日期'] || '',
                'firstFollowDate': row.firstFollowDate || row['首次跟进日期'] || row['初次跟进日期'] || '',

                // 跟进相关字段
                'salesFollow': row.salesFollow || row['转销售跟进'] || row['转销售'] || '',
                'receptionAdvisor': row.receptionAdvisor || row['接待顾问'] || row['接待人员'] || '',
                'followStatus': row.followStatus || row['跟进情况'] || row['跟进状态'] || ''
            });
        }

        // 复制映射后的字段到结果对象
        for (const [key, value] of Object.entries(fieldMapping)) {
            if (value !== null && value !== undefined && String(value).trim() !== '') {
                mappedRow[key] = String(value).trim();
            }
        }

        // 处理布尔值字段
        if (mappedRow.isValid !== undefined) {
            mappedRow.isValid = mappedRow.isValid === '是' || mappedRow.isValid === true || mappedRow.isValid === 'true';
        }
        if (mappedRow.isDuplicate !== undefined) {
            mappedRow.isDuplicate = mappedRow.isDuplicate === '是' || mappedRow.isDuplicate === true || mappedRow.isDuplicate === 'true';
        }
        if (mappedRow.finance !== undefined) {
            mappedRow.finance = mappedRow.finance === '是' || mappedRow.finance === true || mappedRow.finance === 'true';
        }
        if (mappedRow.tradeIn !== undefined) {
            mappedRow.tradeIn = mappedRow.tradeIn === '是' || mappedRow.tradeIn === true || mappedRow.tradeIn === 'true';
        }
        if (mappedRow.wechat !== undefined) {
            mappedRow.wechat = mappedRow.wechat === '是' || mappedRow.wechat === true || mappedRow.wechat === 'true';
        }

        return mappedRow;
    },

    // 格式化日期时间
    formatDateTime: function(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}${month}${day}_${hours}${minutes}${seconds}`;
    },

    // 线索录入日期筛选
    filterLeadsByDate: function() {
        const startDate = document.getElementById('leads-start-date').value;
        const endDate = document.getElementById('leads-end-date').value;

        if (!startDate && !endDate) {
            this.filteredLeadEntries = this.allLeadEntries;
        } else {
            this.filteredLeadEntries = this.allLeadEntries.filter(entry => {
                const entryDate = entry.entryDate;
                if (startDate && endDate) {
                    return entryDate >= startDate && entryDate <= endDate;
                } else if (startDate) {
                    return entryDate >= startDate;
                } else if (endDate) {
                    return entryDate <= endDate;
                }
                return true;
            });
        }

        // 重置分页到第一页
        this.leadsPagination.currentPage = 1;

        // 更新显示
        document.getElementById('all-lead-entries').innerHTML = this.renderLeadsList(this.filteredLeadEntries);
    },

    // 重置线索录入筛选
    resetLeadsFilter: function() {
        document.getElementById('leads-start-date').value = '';
        document.getElementById('leads-end-date').value = '';
        this.filteredLeadEntries = this.allLeadEntries;
        // 重置分页到第一页
        this.leadsPagination.currentPage = 1;
        document.getElementById('all-lead-entries').innerHTML = this.renderLeadsList(this.allLeadEntries);
    },

    // 导出线索录入数据 - 统一化导出功能
    exportLeadsToExcel: function() {
        try {
            const data = this.filteredLeadEntries || this.allLeadEntries;

            if (!data || data.length === 0) {
                alert('没有可导出的线索录入数据');
                return;
            }

            // 显示导出选项模态框
            const modalContent = `
                <div class="export-modal">
                    <h3><i class="fas fa-download"></i> 导出线索录入数据</h3>
                    <div style="margin: 20px 0;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出格式：</label>
                            <label style="margin-right: 20px;">
                                <input type="radio" name="export-format" value="csv" checked> CSV格式
                            </label>
                            <label>
                                <input type="radio" name="export-format" value="excel"> Excel格式
                            </label>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出字段：</label>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                <label><input type="checkbox" value="entryDate" checked> 录入日期</label>
                                <label><input type="checkbox" value="isValid" checked> 是否有效</label>
                                <label><input type="checkbox" value="isDuplicate" checked> 是否重复</label>
                                <label><input type="checkbox" value="customerName" checked> 客户名称</label>
                                <label><input type="checkbox" value="phone" checked> 电话</label>
                                <label><input type="checkbox" value="leadId" checked> 线索ID</label>
                                <label><input type="checkbox" value="smartNumber" checked> 智慧号</label>
                                <label><input type="checkbox" value="intendedModels" checked> 意向车型</label>
                                <label><input type="checkbox" value="region" checked> 区域</label>
                                <label><input type="checkbox" value="wechat" checked> 微信</label>
                                <label><input type="checkbox" value="channel" checked> 渠道</label>
                                <label><input type="checkbox" value="visitDate" checked> 到店日期</label>
                                <label><input type="checkbox" value="dealDate" checked> 成交日期</label>
                                <label><input type="checkbox" value="salesFollow" checked> 转销售跟进</label>
                                <label><input type="checkbox" value="receptionAdvisor" checked> 接待顾问</label>
                                <label><input type="checkbox" value="firstFollowDate" checked> 首次跟进日期</label>
                                <label><input type="checkbox" value="followStatus" checked> 跟进情况</label>
                            </div>
                            <div style="margin-top: 10px;">
                                <button type="button" class="btn btn-sm btn-outline" onclick="customerFunctions.selectAllLeadsFields(true)">全选</button>
                                <button type="button" class="btn btn-sm btn-outline" onclick="customerFunctions.selectAllLeadsFields(false)">全不选</button>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button class="btn btn-primary" id="confirm-export-btn">确认导出</button>
                    </div>
                </div>
            `;

            this.showModal('导出数据', modalContent);

            // 绑定导出确认事件
            document.getElementById('confirm-export-btn').addEventListener('click', () => {
                const format = document.querySelector('input[name="export-format"]:checked').value;
                const selectedFields = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                    .map(cb => cb.value);

                if (selectedFields.length === 0) {
                    alert('请至少选择一个字段');
                    return;
                }

                this.performLeadsExport(format, selectedFields, data);
                const modal = document.querySelector('.modal-overlay');
                if (modal) modal.remove();
            });

        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败: ' + error.message);
        }
    },

    // 全选/全不选线索录入导出字段
    selectAllLeadsFields: function(selectAll) {
        const checkboxes = document.querySelectorAll('input[type="checkbox"][value]');
        checkboxes.forEach(cb => {
            cb.checked = selectAll;
        });
    },

    // 执行线索录入导出
    performLeadsExport: function(format, selectedFields, data) {
        try {
            // 字段名映射
            const fieldNames = {
                entryDate: '录入日期',
                isValid: '是否有效',
                isDuplicate: '是否重复',
                customerName: '客户名称',
                phone: '电话',
                leadId: '线索ID',
                smartNumber: '智慧号',
                intendedModels: '意向车型',
                region: '区域',
                wechat: '微信',
                channel: '渠道',
                visitDate: '到店日期',
                dealDate: '成交日期',
                salesFollow: '转销售跟进',
                receptionAdvisor: '接待顾问',
                firstFollowDate: '首次跟进日期',
                followStatus: '跟进情况'
            };

            // 定义字段顺序
            const fieldOrder = [
                'entryDate', 'isValid', 'isDuplicate', 'customerName', 'phone', 'leadId', 'smartNumber',
                'intendedModels', 'region', 'wechat', 'channel', 'visitDate', 'dealDate',
                'salesFollow', 'receptionAdvisor', 'firstFollowDate', 'followStatus'
            ];

            // 按顺序过滤选中的字段
            const orderedSelectedFields = fieldOrder.filter(field => selectedFields.includes(field));

            // 准备导出数据
            const exportData = data.map(item => {
                const row = {};
                orderedSelectedFields.forEach(field => {
                    let value = item[field] || '';

                    // 特殊处理布尔值字段
                    if (field === 'isValid') {
                        value = item[field] ? '是' : '否';
                    } else if (field === 'isDuplicate') {
                        value = item[field] ? '是' : '否';
                    } else if (field === 'wechat') {
                        value = item[field] === true || item[field] === '是' ? '是' : '否';
                    }

                    row[fieldNames[field]] = value;
                });
                return row;
            });

            if (format === 'csv') {
                this.exportLeadsToCSV(exportData);
            } else {
                this.exportLeadsToExcel_New(exportData);
            }

        } catch (error) {
            console.error('导出处理失败:', error);
            alert('导出处理失败: ' + error.message);
        }
    },

    // 导出线索录入到CSV
    exportLeadsToCSV: function(data) {
        if (data.length === 0) return;

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');

        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `线索录入数据_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('CSV文件导出成功！');
    },

    // 导出线索录入到Excel（新版本）
    exportLeadsToExcel_New: function(data) {
        if (data.length === 0) return;

        try {
            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'LeadEntries');

            // 设置列宽
            const colWidths = Object.keys(data[0]).map(() => ({ wch: 15 }));
            worksheet['!cols'] = colWidths;

            XLSX.writeFile(workbook, `线索录入数据_${new Date().toISOString().split('T')[0]}.xlsx`);
            alert('Excel文件导出成功！');

        } catch (error) {
            console.error('Excel导出失败:', error);
            alert('Excel导出失败，请确保已加载XLSX库: ' + error.message);
        }
    },

    // 导入线索录入Excel
    importLeadsFromExcel: function() {
        const modalContent = `
            <div class="import-modal">
                <h3><i class="fas fa-upload"></i> 导入线索录入数据</h3>
                <div style="margin: 20px 0;">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导入格式：</label>
                        <label style="margin-right: 20px;">
                            <input type="radio" name="leads-import-format" value="excel" checked> Excel格式 (.xlsx, .xls)
                        </label>
                        <label>
                            <input type="radio" name="leads-import-format" value="csv"> CSV格式 (.csv)
                        </label>
                    </div>
                    <p style="color: #6c757d; margin-bottom: 15px;">请确保文件包含以下字段：</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                        <strong>必需字段：</strong> 客户名称, 电话<br>
                        <strong>可选字段：</strong> 录入日期, 是否有效, 线索ID, 智慧号, 意向车型, 区域, 微信, 渠道等
                    </div>
                    <input type="file" id="leads-file-input" accept=".xlsx,.xls,.csv" style="margin-bottom: 15px; width: 100%;">
                    <div id="leads-import-progress" style="display: none; margin-bottom: 15px;">
                        <div style="background: #e9ecef; border-radius: 10px; overflow: hidden;">
                            <div id="leads-import-progress-bar" style="background: #4361ee; height: 20px; width: 0%; transition: width 0.3s;"></div>
                        </div>
                        <div id="leads-import-status" style="margin-top: 5px; font-size: 14px;"></div>
                    </div>
                </div>
                <div style="text-align: right;">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" id="confirm-leads-import-btn" disabled>确认导入</button>
                </div>
            </div>
        `;

        this.showModal(modalContent);

        // 绑定格式选择事件
        document.querySelectorAll('input[name="leads-import-format"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                const fileInput = document.getElementById('leads-file-input');
                if (e.target.value === 'excel') {
                    fileInput.accept = '.xlsx,.xls';
                } else {
                    fileInput.accept = '.csv';
                }
                // 清空已选择的文件
                fileInput.value = '';
                document.getElementById('confirm-leads-import-btn').disabled = true;
            });
        });

        // 绑定文件选择事件
        document.getElementById('leads-file-input').addEventListener('change', (e) => {
            const file = e.target.files[0];
            const confirmBtn = document.getElementById('confirm-leads-import-btn');

            if (file) {
                confirmBtn.disabled = false;
                confirmBtn.onclick = () => this.processLeadsImport(file);
            } else {
                confirmBtn.disabled = true;
            }
        });
    },

    // 处理线索录入导入
    processLeadsImport: function(file) {
        const format = document.querySelector('input[name="leads-import-format"]:checked').value;

        const validateFunction = (data) => {
            // 验证必填字段 - 支持多种字段名称
            const customerName = data.customerName || data['客户名称'] || data['姓名'] || data['客户姓名'];
            const phone = data.phone || data['电话'] || data['手机'] || data['联系电话'] || data['手机号'];

            if (!customerName || !phone) {
                console.error('字段验证失败:', { customerName, phone, originalData: data });
                throw new Error('客户名称和电话为必填字段');
            }

            // 处理布尔值字段
            if (data.isValid !== undefined) {
                data.isValid = data.isValid === '是' || data.isValid === true || data.isValid === 'true';
            }

            return true;
        };

        if (format === 'csv') {
            this.importFromCSV(file, 'leadEntries', validateFunction);
        } else {
            this.importFromExcel(file, 'leadEntries', validateFunction);
        }
    },

    // 渲染展厅录入分页控件
    renderShowroomPagination: function() {
        if (this.showroomPagination.totalPages <= 1) {
            return `<div class="pagination-info">共 ${this.showroomPagination.totalRecords} 条记录</div>`;
        }

        const currentPage = this.showroomPagination.currentPage;
        const totalPages = this.showroomPagination.totalPages;
        const totalRecords = this.showroomPagination.totalRecords;

        // 计算显示的页码范围
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);

        if (endPage - startPage < 4) {
            if (startPage === 1) {
                endPage = Math.min(totalPages, startPage + 4);
            } else if (endPage === totalPages) {
                startPage = Math.max(1, endPage - 4);
            }
        }

        let paginationHtml = `
            <div class="pagination-container" style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                <div class="pagination-info">
                    共 ${totalRecords} 条记录，第 ${currentPage} / ${totalPages} 页
                </div>
                <div class="pagination-controls" style="display: flex; align-items: center; gap: 10px;">
                    <button class="btn btn-sm btn-outline" ${currentPage === 1 ? 'disabled' : ''} onclick="customerFunctions.goToShowroomPage(1)">首页</button>
                    <button class="btn btn-sm btn-outline" ${currentPage === 1 ? 'disabled' : ''} onclick="customerFunctions.goToShowroomPage(${currentPage - 1})">上一页</button>
                    <div class="page-numbers" style="display: flex; gap: 5px;">
        `;

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <button class="btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-outline'}" onclick="customerFunctions.goToShowroomPage(${i})">${i}</button>
            `;
        }

        paginationHtml += `
                    </div>
                    <button class="btn btn-sm btn-outline" ${currentPage === totalPages ? 'disabled' : ''} onclick="customerFunctions.goToShowroomPage(${currentPage + 1})">下一页</button>
                    <button class="btn btn-sm btn-outline" ${currentPage === totalPages ? 'disabled' : ''} onclick="customerFunctions.goToShowroomPage(${totalPages})">末页</button>
                    <div style="display: flex; align-items: center; gap: 5px; margin-left: 15px;">
                        <span>跳转到</span>
                        <input type="number" id="showroom-page-input" min="1" max="${totalPages}" value="${currentPage}" style="width: 60px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; text-align: center;">
                        <button class="btn btn-sm btn-outline" onclick="customerFunctions.jumpToShowroomPage()">确定</button>
                    </div>
                </div>
            </div>
        `;

        return paginationHtml;
    },

    // 渲染线索录入分页控件
    renderLeadsPagination: function() {
        if (this.leadsPagination.totalPages <= 1) {
            return `<div class="pagination-info">共 ${this.leadsPagination.totalRecords} 条记录</div>`;
        }

        const currentPage = this.leadsPagination.currentPage;
        const totalPages = this.leadsPagination.totalPages;
        const totalRecords = this.leadsPagination.totalRecords;

        // 计算显示的页码范围
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);

        if (endPage - startPage < 4) {
            if (startPage === 1) {
                endPage = Math.min(totalPages, startPage + 4);
            } else if (endPage === totalPages) {
                startPage = Math.max(1, endPage - 4);
            }
        }

        let paginationHtml = `
            <div class="pagination-container" style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                <div class="pagination-info">
                    共 ${totalRecords} 条记录，第 ${currentPage} / ${totalPages} 页
                </div>
                <div class="pagination-controls" style="display: flex; align-items: center; gap: 10px;">
                    <button class="btn btn-sm btn-outline" ${currentPage === 1 ? 'disabled' : ''} onclick="customerFunctions.goToLeadsPage(1)">首页</button>
                    <button class="btn btn-sm btn-outline" ${currentPage === 1 ? 'disabled' : ''} onclick="customerFunctions.goToLeadsPage(${currentPage - 1})">上一页</button>
                    <div class="page-numbers" style="display: flex; gap: 5px;">
        `;

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <button class="btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-outline'}" onclick="customerFunctions.goToLeadsPage(${i})">${i}</button>
            `;
        }

        paginationHtml += `
                    </div>
                    <button class="btn btn-sm btn-outline" ${currentPage === totalPages ? 'disabled' : ''} onclick="customerFunctions.goToLeadsPage(${currentPage + 1})">下一页</button>
                    <button class="btn btn-sm btn-outline" ${currentPage === totalPages ? 'disabled' : ''} onclick="customerFunctions.goToLeadsPage(${totalPages})">末页</button>
                    <div style="display: flex; align-items: center; gap: 5px; margin-left: 15px;">
                        <span>跳转到</span>
                        <input type="number" id="leads-page-input" min="1" max="${totalPages}" value="${currentPage}" style="width: 60px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; text-align: center;">
                        <button class="btn btn-sm btn-outline" onclick="customerFunctions.jumpToLeadsPage()">确定</button>
                    </div>
                </div>
            </div>
        `;

        return paginationHtml;
    },

    // 展厅录入分页导航
    goToShowroomPage: function(page) {
        if (page < 1 || page > this.showroomPagination.totalPages) return;
        this.showroomPagination.currentPage = page;
        this.refreshShowroomList();
    },

    // 线索录入分页导航
    goToLeadsPage: function(page) {
        if (page < 1 || page > this.leadsPagination.totalPages) return;
        this.leadsPagination.currentPage = page;
        this.refreshLeadsList();
    },

    // 跳转到指定展厅录入页面
    jumpToShowroomPage: function() {
        const input = document.getElementById('showroom-page-input');
        const page = parseInt(input.value);
        if (page && page >= 1 && page <= this.showroomPagination.totalPages) {
            this.goToShowroomPage(page);
        }
    },

    // 跳转到指定线索录入页面
    jumpToLeadsPage: function() {
        const input = document.getElementById('leads-page-input');
        const page = parseInt(input.value);
        if (page && page >= 1 && page <= this.leadsPagination.totalPages) {
            this.goToLeadsPage(page);
        }
    },

    // 刷新展厅录入列表
    refreshShowroomList: function() {
        const data = this.filteredShowroomEntries || this.allShowroomEntries;
        const listContainer = document.getElementById('all-showroom-entries');
        if (listContainer) {
            listContainer.innerHTML = this.renderShowroomList(data);
        }
    },

    // 刷新线索录入列表
    refreshLeadsList: function() {
        const data = this.filteredLeadEntries || this.allLeadEntries;
        const listContainer = document.getElementById('all-lead-entries');
        if (listContainer) {
            listContainer.innerHTML = this.renderLeadsList(data);
        }
    }
};

// 全局函数
function editCustomer(customerId) {
    alert(`编辑客户 ID: ${customerId} 功能开发中...`);
}

// 展厅录入相关全局函数
function editShowroomEntry(entryId) {
    window.customerFunctions.showShowroomEntryForm(entryId);
}

function deleteShowroomEntry(entryId) {
    if (confirm('确定要删除这条展厅录入记录吗？')) {
        window.dbFunctions.deleteShowroomEntry(entryId)
            .then(() => {
                alert('展厅录入删除成功');
                window.customerFunctions.loadCustomers();
            })
            .catch(error => {
                console.error('删除展厅录入失败:', error);
                alert('删除展厅录入失败: ' + error.message);
            });
    }
}

// 线索录入相关全局函数
function editLeadEntry(entryId) {
    window.customerFunctions.showLeadEntryForm(entryId);
}

function deleteLeadEntry(entryId) {
    if (confirm('确定要删除这条线索录入记录吗？')) {
        window.dbFunctions.deleteLeadEntry(entryId)
            .then(() => {
                alert('线索录入删除成功');
                window.customerFunctions.loadCustomers();
            })
            .catch(error => {
                console.error('删除线索录入失败:', error);
                alert('删除线索录入失败: ' + error.message);
            });
    }
}

// 扩展customerFunctions对象，添加列设置功能
Object.assign(window.customerFunctions, {
    // 显示展厅录入列设置
    showShowroomColumnSettings: function() {
        const modalContent = `
            <div class="column-settings-modal">
                <h3><i class="fas fa-columns"></i> 展厅录入列设置</h3>
                <div style="margin: 20px 0;">
                    <p style="color: #6c757d; margin-bottom: 15px;">选择要显示的列：</p>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; max-height: 300px; overflow-y: auto;">
                        ${this.renderColumnCheckboxes('showroom')}
                    </div>
                </div>
                <div style="text-align: right;">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" onclick="customerFunctions.applyShowroomColumnSettings()" style="margin-left: 10px;">应用设置</button>
                </div>
            </div>
        `;

        this.showModal('列设置', modalContent);
    },

    // 显示线索录入列设置
    showLeadsColumnSettings: function() {
        const modalContent = `
            <div class="column-settings-modal">
                <h3><i class="fas fa-columns"></i> 线索录入列设置</h3>
                <div style="margin: 20px 0;">
                    <p style="color: #6c757d; margin-bottom: 15px;">选择要显示的列：</p>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; max-height: 300px; overflow-y: auto;">
                        ${this.renderColumnCheckboxes('leads')}
                    </div>
                </div>
                <div style="text-align: right;">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" onclick="customerFunctions.applyLeadsColumnSettings()" style="margin-left: 10px;">应用设置</button>
                </div>
            </div>
        `;

        this.showModal('列设置', modalContent);
    },

    // 渲染列复选框
    renderColumnCheckboxes: function(type) {
        const fieldNames = type === 'showroom' ? {
            entryDate: '录入日期',
            entryPersonnel: '录入人员',
            salesAdvisor: '销售顾问',
            stayDuration: '滞店时间',
            customerName: '客户名称',
            phone: '电话',
            visitType: '来店类型',
            region: '区域',
            intention: '意向',
            intendedModels: '意向车型',
            testDrive: '试驾',
            finance: '金融',
            tradeIn: '置换',
            quote: '报价'
        } : {
            entryDate: '录入日期',
            isValid: '是否有效',
            customerName: '客户名称',
            phone: '电话',
            leadId: '线索ID',
            smartNumber: '智慧号',
            intendedModels: '意向车型',
            region: '区域',
            wechat: '微信',
            channel: '渠道',
            visitDate: '到店日期',
            dealDate: '成交日期',
            salesFollow: '转销售跟进',
            receptionAdvisor: '接待顾问',
            firstFollowDate: '首次跟进日期',
            followStatus: '跟进情况'
        };

        const settings = type === 'showroom' ? this.showroomColumnSettings : this.leadsColumnSettings;

        return Object.entries(fieldNames).map(([key, name]) => `
            <label style="display: flex; align-items: center; padding: 5px;">
                <input type="checkbox" value="${key}" ${settings[key] ? 'checked' : ''} style="margin-right: 8px;">
                ${name}
            </label>
        `).join('');
    },

    // 应用展厅录入列设置
    applyShowroomColumnSettings: function() {
        const checkboxes = document.querySelectorAll('.column-settings-modal input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            this.showroomColumnSettings[checkbox.value] = checkbox.checked;
        });

        // 保存到localStorage
        localStorage.setItem('showroomColumnSettings', JSON.stringify(this.showroomColumnSettings));

        // 关闭模态框
        const modal = document.querySelector('.modal-overlay');
        if (modal) modal.remove();

        // 重新渲染表格
        this.switchTab('showroom');
    },

    // 应用线索录入列设置
    applyLeadsColumnSettings: function() {
        const checkboxes = document.querySelectorAll('.column-settings-modal input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            this.leadsColumnSettings[checkbox.value] = checkbox.checked;
        });

        // 保存到localStorage
        localStorage.setItem('leadsColumnSettings', JSON.stringify(this.leadsColumnSettings));

        // 关闭模态框
        const modal = document.querySelector('.modal-overlay');
        if (modal) modal.remove();

        // 重新渲染表格
        this.switchTab('leads');
    },

    // 从localStorage加载列设置
    loadColumnSettings: function() {
        const showroomSettings = localStorage.getItem('showroomColumnSettings');
        if (showroomSettings) {
            this.showroomColumnSettings = { ...this.showroomColumnSettings, ...JSON.parse(showroomSettings) };
        }

        const leadsSettings = localStorage.getItem('leadsColumnSettings');
        if (leadsSettings) {
            this.leadsColumnSettings = { ...this.leadsColumnSettings, ...JSON.parse(leadsSettings) };
        }
    }
});

