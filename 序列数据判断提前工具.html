<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>序列数据处理工具 - 条件提取内容</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #2a9d8f);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .container {
            width: 100%;
            max-width: 1100px;
            margin: 20px auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(to right, #2a9d8f, #1a5f7a);
            color: white;
            padding: 25px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><path d="M0,0 L100,0 L100,100 Z" fill="rgba(255,255,255,0.1)"/></svg>');
            background-size: 100% 100%;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
            position: relative;
        }
        
        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            position: relative;
        }
        
        .content {
            padding: 30px;
        }
        
        .input-section, .output-section {
            margin-bottom: 30px;
        }
        
        h2 {
            color: #1a5f7a;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #2a9d8f;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        h2 i {
            font-size: 1.4rem;
        }
        
        .input-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .input-group {
            display: flex;
            flex-direction: column;
        }
        
        .input-group label {
            margin-bottom: 8px;
            font-weight: 600;
            color: #1a5f7a;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        textarea {
            width: 100%;
            height: 200px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            resize: vertical;
            font-size: 16px;
            transition: all 0.3s;
            background: #f8f9ff;
            line-height: 1.6;
        }
        
        textarea:focus {
            border-color: #2a9d8f;
            outline: none;
            box-shadow: 0 0 0 3px rgba(42, 157, 143, 0.2);
            background: #fff;
        }
        
        .note {
            background: #e8f4f3;
            padding: 12px 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-size: 0.9rem;
            color: #1a5f7a;
            border-left: 4px solid #2a9d8f;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(to right, #2a9d8f, #1a5f7a);
            color: white;
            flex: 1;
        }
        
        .btn-secondary {
            background: #f1f1f1;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary:hover {
            background: linear-gradient(to right, #239b8d, #164e65);
        }
        
        .btn-secondary:hover {
            background: #e5e5e5;
        }
        
        .result-container {
            overflow-x: auto;
            margin-top: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.05);
        }
        
        .result-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 600px;
        }
        
        .result-table th {
            background: linear-gradient(to bottom, #2a9d8f, #1a5f7a);
            color: white;
            padding: 12px 15px;
            text-align: left;
        }
        
        .result-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .result-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .result-table tr:hover {
            background-color: #e8f4f3;
        }
        
        .result-table .yes {
            color: #27ae60;
            font-weight: bold;
        }
        
        .result-table .no {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .result-table .extracted {
            color: #1a5f7a;
            font-weight: 500;
        }
        
        .result-table .empty {
            color: #95a5a6;
            font-style: italic;
        }
        
        .copy-btn {
            padding: 10px 20px;
            background: rgba(42, 157, 143, 0.1);
            color: #1a5f7a;
            border: 1px solid #2a9d8f;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-top: 15px;
            font-weight: 600;
        }
        
        .copy-btn:hover {
            background: rgba(42, 157, 143, 0.2);
        }
        
        footer {
            text-align: center;
            padding: 20px;
            color: #777;
            font-size: 0.9rem;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }
        
        .example {
            background: #e8f4f3;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-size: 0.9rem;
            border-left: 4px solid #2a9d8f;
        }
        
        .example h3 {
            color: #1a5f7a;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .example-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .example-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .example-item .rule {
            display: grid;
            grid-template-columns: 30px 1fr 1fr;
            gap: 10px;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px dashed #eee;
        }
        
        .example-item .rule:last-child {
            border-bottom: none;
        }
        
        .example-item .header {
            font-weight: bold;
            color: #1a5f7a;
        }
        
        .success-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transform: translateX(200%);
            transition: transform 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .success-message.show {
            transform: translateX(0);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .input-container {
                grid-template-columns: 1fr;
            }
            
            .buttons {
                flex-direction: column;
            }
            
            .example-content {
                grid-template-columns: 1fr;
            }
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .logo-icon {
            background: linear-gradient(to right, #2a9d8f, #1a5f7a);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div class="logo">
        <div class="logo-icon">
            <i class="fas fa-filter"></i>
        </div>
        <h1>序列数据处理工具</h1>
    </div>
    
    <div class="container">
        <header>
            <h1>条件提取内容工具</h1>
            <p class="subtitle">根据第一列的"是/否"决定是否保留第二列内容到第三列</p>
        </header>
        
        <div class="content">
            <section class="input-section">
                <h2><i class="fas fa-database"></i> 输入数据</h2>
                
                <div class="input-container">
                    <div class="input-group">
                        <label for="sequence1"><i class="fas fa-list-ol"></i> 第一序列 (是/否)</label>
                        <textarea id="sequence1" placeholder="请输入第一序列数据（是/否）&#10;每行一个值&#10;例如：&#10;是&#10;否&#10;是"></textarea>
                    </div>
                    
                    <div class="input-group">
                        <label for="sequence2"><i class="fas fa-font"></i> 第二序列 (内容)</label>
                        <textarea id="sequence2" placeholder="请输入第二序列数据（内容）&#10;每行一个值&#10;例如：&#10;苹果&#10;香蕉&#10;橙子"></textarea>
                    </div>
                </div>
                
                <div class="note">
                    <strong>处理规则：</strong> 
                    当第一序列为"是"时，将第二序列内容提取到第三序列；当第一序列为"否"时，第三序列留空
                </div>
                
                <div class="buttons">
                    <button class="btn btn-primary" onclick="processData()">
                        <i class="fas fa-cogs"></i>
                        处理数据
                    </button>
                    <button class="btn btn-secondary" onclick="clearAll()">
                        <i class="fas fa-trash-alt"></i>
                        清空全部
                    </button>
                    <button class="btn btn-secondary" onclick="loadExample()">
                        <i class="fas fa-lightbulb"></i>
                        加载示例
                    </button>
                </div>
            </section>
            
            <section class="output-section">
                <h2><i class="fas fa-file-alt"></i> 处理结果</h2>
                
                <div class="result-container">
                    <table class="result-table">
                        <thead>
                            <tr>
                                <th width="20%">第一序列 (是/否)</th>
                                <th width="40%">第二序列 (内容)</th>
                                <th width="40%">第三序列 (提取结果)</th>
                            </tr>
                        </thead>
                        <tbody id="resultBody">
                            <tr>
                                <td colspan="3" style="text-align: center; padding: 30px; color: #95a5a6;">
                                    处理结果将显示在这里
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <button class="copy-btn" onclick="copyResults()">
                    <i class="fas fa-copy"></i>
                    复制第三序列结果
                </button>
            </section>
            
            <div class="example">
                <h3><i class="fas fa-info-circle"></i> 处理规则说明</h3>
                <div class="example-content">
                    <div class="example-item">
                        <div class="rule header">
                            <div>#</div>
                            <div>第一序列</div>
                            <div>第二序列 → 第三序列</div>
                        </div>
                        <div class="rule">
                            <div><i class="fas fa-check yes"></i></div>
                            <div class="yes">是</div>
                            <div>内容 → <span class="extracted">内容</span></div>
                        </div>
                        <div class="rule">
                            <div><i class="fas fa-times no"></i></div>
                            <div class="no">否</div>
                            <div>内容 → <span class="empty">（空）</span></div>
                        </div>
                    </div>
                    
                    <div class="example-item">
                        <div class="rule header">
                            <div>#</div>
                            <div>示例输入</div>
                            <div>输出结果</div>
                        </div>
                        <div class="rule">
                            <div>1</div>
                            <div>是</div>
                            <div>苹果 → <span class="extracted">苹果</span></div>
                        </div>
                        <div class="rule">
                            <div>2</div>
                            <div>否</div>
                            <div>香蕉 → <span class="empty">（空）</span></div>
                        </div>
                        <div class="rule">
                            <div>3</div>
                            <div>是</div>
                            <div>橙子 → <span class="extracted">橙子</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <footer>
            <p>© 2023 序列数据处理工具 | 根据条件提取内容</p>
        </footer>
    </div>
    
    <div class="success-message" id="successMessage">
        <i class="fas fa-check-circle"></i>
        第三序列结果已复制到剪贴板！
    </div>
    
    <script>
        function processData() {
            const seq1 = document.getElementById('sequence1').value.split('\n');
            const seq2 = document.getElementById('sequence2').value.split('\n');
            const resultBody = document.getElementById('resultBody');
            
            // 清空结果表格
            resultBody.innerHTML = '';
            
            // 获取最大行数
            const maxRows = Math.max(seq1.length, seq2.length);
            
            if (maxRows === 0) {
                resultBody.innerHTML = `<tr><td colspan="3" style="text-align: center; padding: 30px; color: #95a5a6;">请输入数据</td></tr>`;
                return;
            }
            
            // 处理每一行数据
            for (let i = 0; i < maxRows; i++) {
                const row = document.createElement('tr');
                
                // 第一列
                const cell1 = document.createElement('td');
                const val1 = (i < seq1.length) ? seq1[i].trim() : '';
                cell1.textContent = val1;
                if (val1 === '是') {
                    cell1.className = 'yes';
                } else if (val1 === '否') {
                    cell1.className = 'no';
                }
                
                // 第二列
                const cell2 = document.createElement('td');
                const val2 = (i < seq2.length) ? seq2[i].trim() : '';
                cell2.textContent = val2;
                
                // 第三列
                const cell3 = document.createElement('td');
                if (val1 === '是' && val2) {
                    cell3.textContent = val2;
                    cell3.className = 'extracted';
                } else if (val1 === '否') {
                    cell3.textContent = '';
                    cell3.className = 'empty';
                } else {
                    cell3.textContent = '';
                }
                
                row.appendChild(cell1);
                row.appendChild(cell2);
                row.appendChild(cell3);
                resultBody.appendChild(row);
            }
        }
        
        function clearAll() {
            document.getElementById('sequence1').value = '';
            document.getElementById('sequence2').value = '';
            document.getElementById('resultBody').innerHTML = 
                `<tr><td colspan="3" style="text-align: center; padding: 30px; color: #95a5a6;">处理结果将显示在这里</td></tr>`;
        }
        
        function loadExample() {
            const seq1 = 
                `是
否
是
否
是
是
否`;
            
            const seq2 = 
                `苹果
香蕉
橙子
葡萄
西瓜
桃子
芒果`;
            
            document.getElementById('sequence1').value = seq1;
            document.getElementById('sequence2').value = seq2;
            
            // 自动处理数据
            setTimeout(processData, 300);
        }
        
        function copyResults() {
            const resultRows = document.querySelectorAll('#resultBody tr');
            if (resultRows.length === 0) return;
            
            let output = '';
            resultRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 3) {
                    output += cells[2].textContent + '\n';
                }
            });
            
            if (output) {
                navigator.clipboard.writeText(output).then(() => {
                    // 显示成功消息
                    const message = document.getElementById('successMessage');
                    message.classList.add('show');
                    
                    setTimeout(() => {
                        message.classList.remove('show');
                    }, 2000);
                });
            }
        }
        
        // 初始加载示例
        window.onload = loadExample;
    </script>
</body>
</html>