// 扩展映射表测试脚本 - 验证新增的姓氏和名字字符映射

// 从orderModule.js复制完整的扩展映射表
function chineseToPinyin(chinese) {
    // 中文字符到拼音首字母的映射表（常见姓名字符）
    const pinyinFirstLetterMap = {
        // 常见姓氏（按拼音首字母排序）
        // A-B
        '安': 'A', '艾': 'A', '敖': 'A', '鲍': 'B', '包': 'B', '白': 'B', '毕': 'B', '卜': 'B', '卞': 'B', '边': 'B', '薄': 'B',
        // C-D  
        '陈': 'C', '曹': 'C', '蔡': 'C', '崔': 'C', '程': 'C', '常': 'C', '褚': 'C', '车': 'C', '成': 'C', '池': 'C', '楚': 'C',
        '丁': 'D', '邓': 'D', '杜': 'D', '董': 'D', '戴': 'D', '段': 'D', '窦': 'D', '邸': 'D', '刁': 'D', '狄': 'D', '杜': 'D',
        // F-G
        '冯': 'F', '付': 'F', '方': 'F', '范': 'F', '费': 'F', '傅': 'F', '樊': 'F', '封': 'F', '房': 'F', '丰': 'F', '凤': 'F',
        '郭': 'G', '高': 'G', '顾': 'G', '龚': 'G', '关': 'G', '甘': 'G', '葛': 'G', '耿': 'G', '宫': 'G', '巩': 'G', '古': 'G',
        // H-J
        '黄': 'H', '何': 'H', '胡': 'H', '韩': 'H', '侯': 'H', '贺': 'H', '郝': 'H', '洪': 'H', '华': 'H', '霍': 'H', '花': 'H',
        '江': 'J', '金': 'J', '姜': 'J', '蒋': 'J', '贾': 'J', '纪': 'J', '季': 'J', '焦': 'J', '靳': 'J', '景': 'J', '居': 'J',
        // K-L
        '孔': 'K', '康': 'K', '柯': 'K', '匡': 'K', '况': 'K', '邝': 'K', '孔': 'K', '寇': 'K', '库': 'K', '蒯': 'K', '阚': 'K',
        '李': 'L', '刘': 'L', '林': 'L', '梁': 'L', '罗': 'L', '陆': 'L', '卢': 'L', '吕': 'L', '廖': 'L', '雷': 'L', '黎': 'L',
        '龙': 'L', '娄': 'L', '柳': 'L', '路': 'L', '栗': 'L', '连': 'L', '蓝': 'L', '兰': 'L', '赖': 'L', '来': 'L', '乐': 'L',
        // M-P
        '马': 'M', '毛': 'M', '孟': 'M', '莫': 'M', '苗': 'M', '穆': 'M', '梅': 'M', '米': 'M', '闵': 'M', '明': 'M', '牟': 'M',
        '倪': 'N', '聂': 'N', '牛': 'N', '宁': 'N', '南': 'N', '年': 'N', '农': 'N', '那': 'N', '能': 'N', '乜': 'N', '钮': 'N',
        '欧': 'O', '欧阳': 'O', '区': 'O', '殴': 'O', '瓯': 'O',
        '潘': 'P', '彭': 'P', '裴': 'P', '皮': 'P', '平': 'P', '蒲': 'P', '濮': 'P', '朴': 'P', '逄': 'P', '盘': 'P', '庞': 'P',
        // Q-Z
        '邱': 'Q', '秦': 'Q', '钱': 'Q', '齐': 'Q', '乔': 'Q', '覃': 'Q', '祁': 'Q', '戚': 'Q', '强': 'Q', '屈': 'Q', '曲': 'Q',
        '任': 'R', '阮': 'R', '饶': 'R', '容': 'R', '荣': 'R', '冉': 'R', '芮': 'R', '汝': 'R', '茹': 'R', '阮': 'R', '瑞': 'R',
        '孙': 'S', '沈': 'S', '宋': 'S', '苏': 'S', '石': 'S', '史': 'S', '邵': 'S', '施': 'S', '司': 'S', '申': 'S', '盛': 'S',
        '唐': 'T', '田': 'T', '陶': 'T', '谭': 'T', '汤': 'T', '滕': 'T', '童': 'T', '涂': 'T', '屠': 'T', '谈': 'T', '台': 'T',
        '万': 'W', '王': 'W', '吴': 'W', '魏': 'W', '汪': 'W', '武': 'W', '韦': 'W', '温': 'W', '文': 'W', '翁': 'W', '邬': 'W',
        '许': 'X', '谢': 'X', '徐': 'X', '薛': 'X', '萧': 'X', '夏': 'X', '熊': 'X', '向': 'X', '邢': 'X', '辛': 'X', '席': 'X',
        '杨': 'Y', '叶': 'Y', '于': 'Y', '袁': 'Y', '姚': 'Y', '余': 'Y', '尹': 'Y', '闫': 'Y', '严': 'Y', '殷': 'Y', '易': 'Y',
        '张': 'Z', '赵': 'Z', '周': 'Z', '朱': 'Z', '郑': 'Z', '钟': 'Z', '曾': 'Z', '左': 'Z', '邹': 'Z', '庄': 'Z', '卓': 'Z',

        // 常见名字字符（按拼音首字母排序）
        // 数字
        '一': 'Y', '二': 'E', '三': 'S', '四': 'S', '五': 'W', '六': 'L', '七': 'Q', '八': 'B', '九': 'J', '十': 'S',
        
        // A-Z（精选常用字符）
        '安': 'A', '爱': 'A', '奥': 'A', '昂': 'A', '傲': 'A', '澳': 'A', '艾': 'A', '阿': 'A', '啊': 'A', '按': 'A',
        '斌': 'B', '彬': 'B', '宾': 'B', '冰': 'B', '兵': 'B', '波': 'B', '博': 'B', '柏': 'B', '白': 'B', '百': 'B',
        '超': 'C', '成': 'C', '诚': 'C', '城': 'C', '晨': 'C', '辰': 'C', '春': 'C', '纯': 'C', '聪': 'C', '翠': 'C',
        '德': 'D', '东': 'D', '冬': 'D', '栋': 'D', '丹': 'D', '旦': 'D', '道': 'D', '达': 'D', '大': 'D', '代': 'D',
        '恩': 'E', '儿': 'E', '尔': 'E', '二': 'E', '峨': 'E', '娥': 'E', '额': 'E', '鹅': 'E', '俄': 'E', '饿': 'E',
        '芳': 'F', '方': 'F', '飞': 'F', '非': 'F', '菲': 'F', '斐': 'F', '丰': 'F', '风': 'F', '峰': 'F', '锋': 'F',
        '国': 'G', '光': 'G', '广': 'G', '贵': 'G', '桂': 'G', '功': 'G', '公': 'G', '刚': 'G', '钢': 'G', '高': 'G',
        '华': 'H', '花': 'H', '辉': 'H', '慧': 'H', '惠': 'H', '会': 'H', '海': 'H', '涵': 'H', '寒': 'H', '汉': 'H',
        '杰': 'J', '健': 'J', '建': 'J', '江': 'J', '金': 'J', '锦': 'J', '进': 'J', '晶': 'J', '静': 'J', '敬': 'J',
        '康': 'K', '凯': 'K', '开': 'K', '科': 'K', '可': 'K', '克': 'K', '客': 'K', '课': 'K', '快': 'K', '宽': 'K',
        '丽': 'L', '利': 'L', '力': 'L', '立': 'L', '理': 'L', '礼': 'L', '李': 'L', '里': 'L', '离': 'L', '黎': 'L',
        '明': 'M', '民': 'M', '敏': 'M', '名': 'M', '命': 'M', '鸣': 'M', '铭': 'M', '茗': 'M', '冥': 'M', '溟': 'M',
        '娜': 'N', '那': 'N', '哪': 'N', '纳': 'N', '拿': 'N', '南': 'N', '男': 'N', '难': 'N', '囊': 'N', '挠': 'N',
        '平': 'P', '萍': 'P', '苹': 'P', '屏': 'P', '乒': 'P', '坪': 'P', '评': 'P', '凭': 'P', '瓶': 'P', '品': 'P',
        '奇': 'Q', '齐': 'Q', '其': 'Q', '起': 'Q', '气': 'Q', '器': 'Q', '千': 'Q', '前': 'Q', '钱': 'Q', '潜': 'Q',
        '人': 'R', '仁': 'R', '任': 'R', '认': 'R', '忍': 'R', '韧': 'R', '刃': 'R', '润': 'R', '闰': 'R', '若': 'R',
        '山': 'S', '水': 'S', '石': 'S', '树': 'S', '森': 'S', '松': 'S', '顺': 'S', '思': 'S', '四': 'S', '丝': 'S',
        '天': 'T', '田': 'T', '甜': 'T', '填': 'T', '挑': 'T', '条': 'T', '跳': 'T', '贴': 'T', '铁': 'T', '听': 'T',
        '文': 'W', '武': 'W', '伟': 'W', '维': 'W', '为': 'W', '位': 'W', '味': 'W', '谓': 'W', '尾': 'W', '纬': 'W',
        '晓': 'X', '小': 'X', '孝': 'X', '校': 'X', '肖': 'X', '消': 'X', '宵': 'X', '淆': 'X', '心': 'X', '新': 'X',
        '雅': 'Y', '亚': 'Y', '讶': 'Y', '焉': 'Y', '咽': 'Y', '阉': 'Y', '烟': 'Y', '淹': 'Y', '盐': 'Y', '严': 'Y',
        '志': 'Z', '智': 'Z', '制': 'Z', '治': 'Z', '中': 'Z', '忠': 'Z', '钟': 'Z', '终': 'Z', '种': 'Z', '重': 'Z',
        
        // 特殊字符（之前修复的）
        '洁': 'J', '晓': 'X', '瑜': 'Y', '佳': 'J', '颖': 'Y', '诚': 'C', '健': 'J', '忍': 'R', '斌': 'B'
    };

    if (!chinese) return 'XXX';

    let result = '';
    // 处理姓名，通常取前3个字符（姓+名的前两个字）
    for (let i = 0; i < Math.min(chinese.length, 3); i++) {
        const char = chinese[i];
        if (pinyinFirstLetterMap[char]) {
            result += pinyinFirstLetterMap[char];
        } else if (/[A-Za-z]/.test(char)) {
            // 如果是英文字母，直接使用大写
            result += char.toUpperCase();
        } else {
            // 未知字符使用X代替
            result += 'X';
        }
    }

    // 确保结果始终为3位，不足的用X填充
    return result.padEnd(3, 'X').substring(0, 3);
}

function generateOrderNumber(advisorName, orderDate) {
    const date = new Date(orderDate);
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const advisorCode = chineseToPinyin(advisorName);
    
    // 模拟序号（实际应用中从数据库获取）
    const sequence = '01';
    
    return `${year}${month}${day}${advisorCode}${sequence}`;
}

// 测试新增的姓氏映射
console.log('=== 扩展映射表测试 ===');
console.log('');

console.log('新增姓氏映射测试:');
const newSurnames = [
    { name: '傅志强', expected: 'FZQ', description: '傅姓测试' },
    { name: '邢美丽', expected: 'XML', description: '邢姓测试' },
    { name: '卜小明', expected: 'BXM', description: '卜姓测试' },
    { name: '关羽', expected: 'GYX', description: '关姓测试' },
    { name: '鲍鱼', expected: 'BYX', description: '鲍姓测试' },
    { name: '包青天', expected: 'BQT', description: '包姓测试' },
    { name: '左宗棠', expected: 'ZZT', description: '左姓测试' },
    { name: '褚时健', expected: 'CSJ', description: '褚姓测试' },
    { name: '卫青', expected: 'WQX', description: '卫姓测试' },
    { name: '费玉清', expected: 'FYQ', description: '费姓测试' }
];

console.log('姓名\t\t实际结果\t预期结果\t状态\t\t说明');
console.log('--------------------------------------------------------');

let surnameCorrect = 0;
newSurnames.forEach(testCase => {
    const result = chineseToPinyin(testCase.name);
    const isCorrect = result === testCase.expected;
    const status = isCorrect ? '✓ 正确' : '✗ 错误';
    
    if (isCorrect) surnameCorrect++;
    
    console.log(`${testCase.name}\t\t${result}\t\t${testCase.expected}\t\t${status}\t\t${testCase.description}`);
});

console.log(`\n姓氏测试结果: ${surnameCorrect}/${newSurnames.length} (${((surnameCorrect/newSurnames.length)*100).toFixed(1)}%)`);
console.log('');

// 测试新增的名字字符映射
console.log('新增名字字符映射测试:');
const newNameChars = [
    { name: '李霞', expected: 'LXX', description: '霞字测试' },
    { name: '王鹏', expected: 'WPX', description: '鹏字测试' },
    { name: '张辉', expected: 'ZHX', description: '辉字测试' },
    { name: '陈宇', expected: 'CYX', description: '宇字测试' },
    { name: '刘飞', expected: 'LFX', description: '飞字测试' },
    { name: '杨凤', expected: 'YFX', description: '凤字测试' },
    { name: '赵玲', expected: 'ZLX', description: '玲字测试' },
    { name: '孙琳', expected: 'SLX', description: '琳字测试' },
    { name: '周琴', expected: 'ZQX', description: '琴字测试' },
    { name: '吴琼', expected: 'WQX', description: '琼字测试' },
    { name: '郑瑶', expected: 'ZYX', description: '瑶字测试' },
    { name: '王璐', expected: 'WLX', description: '璐字测试' },
    { name: '李婷', expected: 'LTX', description: '婷字测试' },
    { name: '张婕', expected: 'ZJX', description: '婕字测试' },
    { name: '陈婉', expected: 'CWX', description: '婉字测试' },
    { name: '刘雯', expected: 'LWX', description: '雯字测试' },
    { name: '杨雅', expected: 'YYX', description: '雅字测试' },
    { name: '赵雁', expected: 'ZYX', description: '雁字测试' },
    { name: '孙露', expected: 'SLX', description: '露字测试' },
    { name: '周霖', expected: 'ZLX', description: '霖字测试' }
];

console.log('姓名\t\t实际结果\t预期结果\t状态\t\t说明');
console.log('--------------------------------------------------------');

let nameCharCorrect = 0;
newNameChars.forEach(testCase => {
    const result = chineseToPinyin(testCase.name);
    const isCorrect = result === testCase.expected;
    const status = isCorrect ? '✓ 正确' : '✗ 错误';
    
    if (isCorrect) nameCharCorrect++;
    
    console.log(`${testCase.name}\t\t${result}\t\t${testCase.expected}\t\t${status}\t\t${testCase.description}`);
});

console.log(`\n名字字符测试结果: ${nameCharCorrect}/${newNameChars.length} (${((nameCharCorrect/newNameChars.length)*100).toFixed(1)}%)`);
console.log('');

// 订单号生成测试
console.log('订单号生成测试（新增字符）:');
const testDate = '2025-07-26';
const allTestCases = [...newSurnames, ...newNameChars];

console.log('姓名\t\t拼音代码\t订单号\t\t\t长度\t状态');
console.log('--------------------------------------------------------');

let orderCorrect = 0;
allTestCases.slice(0, 10).forEach(testCase => { // 只测试前10个
    const pinyinCode = chineseToPinyin(testCase.name);
    const orderNumber = generateOrderNumber(testCase.name, testDate);
    const expectedOrderNumber = `250726${testCase.expected}01`;
    const isCorrect = orderNumber === expectedOrderNumber && orderNumber.length === 11;
    const status = isCorrect ? '✓ 正确' : '✗ 错误';
    
    if (isCorrect) orderCorrect++;
    
    console.log(`${testCase.name}\t\t${pinyinCode}\t\t${orderNumber}\t${orderNumber.length}位\t${status}`);
});

console.log(`\n订单号测试结果: ${orderCorrect}/10 (${(orderCorrect/10*100).toFixed(1)}%)`);
console.log('');

// 总体统计
const totalTests = newSurnames.length + newNameChars.length;
const totalCorrect = surnameCorrect + nameCharCorrect;

console.log('=== 总体测试统计 ===');
console.log(`姓氏映射测试: ${surnameCorrect}/${newSurnames.length} (${((surnameCorrect/newSurnames.length)*100).toFixed(1)}%)`);
console.log(`名字字符测试: ${nameCharCorrect}/${newNameChars.length} (${((nameCharCorrect/newNameChars.length)*100).toFixed(1)}%)`);
console.log(`订单号生成测试: ${orderCorrect}/10 (${(orderCorrect/10*100).toFixed(1)}%)`);
console.log(`总体通过率: ${totalCorrect}/${totalTests} (${((totalCorrect/totalTests)*100).toFixed(1)}%)`);
console.log('');

if (totalCorrect === totalTests) {
    console.log('🎉 所有测试通过！扩展映射表工作正常。');
} else {
    console.log('⚠️  部分测试失败，需要检查映射表。');
}

console.log('');
console.log('=== 测试完成 ===');
