<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>配件库管理 - 汽车销售漏斗管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dexie@3.2.2/dist/dexie.min.js"></script>
    <script src="xlsx.full.min.js"></script>
    <link rel="stylesheet" href="styles.css" />
</head>
<body style="height: 100vh; margin: 0;">
    <div class="container" style="flex: 1; display: flex; flex-direction: column; height: 100%;">
        <header style="background: linear-gradient(135deg, #4361ee, #3f37c9); color: white; padding: 15px 30px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
            <div class="logo" style="display: flex; align-items: center; gap: 15px;">
                <i class="fas fa-boxes" style="font-size: 28px;"></i>
                <h1 style="font-size: 22px; font-weight: 600;">配件库管理</h1>
            </div>
            <div class="user-info" style="display: flex; align-items: center; gap: 12px;">
                <div class="user-avatar" style="width: 42px; height: 42px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 18px;">张</div>
                <div>
                    <div>张经理</div>
                    <div style="font-size: 13px; opacity: 0.8;">销售主管</div>
                </div>
            </div>
        </header>
        
        <div class="main-content" style="flex: 1; display: flex; overflow: hidden;">
            <div class="sidebar" style="width: 260px; background: white; padding: 25px 20px; display: flex; flex-direction: column; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); z-index: 10; height: 100%; overflow-y: auto;">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 13px; color: #6c757d; margin-bottom: 5px;">配件总数</div>
                    <div style="font-size: 28px; font-weight: 700; color: #3f37c9;" id="total-parts">0</div>
                    <div style="font-size: 14px; color: #6c757d;">库存配件</div>
                </div>
                
                <ul class="nav-menu" style="list-style: none; margin-top: 20px;">
                    <li style="margin-bottom: 8px;"><a href="index.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-home" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 首页</a></li>
                    <li style="margin-bottom: 8px;"><a href="salesanalytics.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-chart-line" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 销售分析</a></li>
                    <li style="margin-bottom: 8px;"><a href="customers.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 客户管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="order.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-shopping-cart" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 订单管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="parts.html" class="active" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #4361ee; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-boxes" style="margin-right: 12px; width: 20px; text-align: center; color: #4361ee;"></i> 配件库管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="settings.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 系统设置</a></li>
                    <li style="margin-bottom: 8px;"><a href="user.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 用户管理</a></li>
                </ul>
                
                <div style="margin-top: 30px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h3 style="font-size: 15px; margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-chart-bar" style="color: #3f37c9;"></i> 库存统计
                    </h3>
                    <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 8px;">
                        <span style="color: #6c757d;">可用库存:</span>
                        <span style="font-weight: 600; color: #28a745;" id="available-stock">0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 8px;">
                        <span style="color: #6c757d;">预订库存:</span>
                        <span style="font-weight: 600; color: #ffc107;" id="reserved-stock">0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 8px;">
                        <span style="color: #6c757d;">借出库存:</span>
                        <span style="font-weight: 600; color: #dc3545;" id="lent-stock">0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 8px;">
                        <span style="color: #6c757d;">待匹配需求:</span>
                        <span style="font-weight: 600; color: #6f42c1;" id="pending-requests">0</span>
                    </div>
                </div>
            </div>
            
            <div class="content-area" style="flex: 1; padding: 30px; overflow-y: auto;">
                <!-- 标签页导航 -->
                <div class="tab-navigation" style="display: flex; border-bottom: 2px solid #e9ecef; margin-bottom: 30px;">
                    <button class="tab-btn active" data-tab="requests" style="padding: 12px 24px; border: none; background: none; font-size: 16px; font-weight: 500; color: #4361ee; border-bottom: 2px solid #4361ee; cursor: pointer; transition: all 0.3s ease;">
                        <i class="fas fa-clipboard-list" style="margin-right: 8px;"></i>订单需求
                    </button>
                    <button class="tab-btn" data-tab="inbound" style="padding: 12px 24px; border: none; background: none; font-size: 16px; font-weight: 500; color: #6c757d; border-bottom: 2px solid transparent; cursor: pointer; transition: all 0.3s ease;">
                        <i class="fas fa-arrow-down" style="margin-right: 8px;"></i>入库管理
                    </button>
                    <button class="tab-btn" data-tab="outbound" style="padding: 12px 24px; border: none; background: none; font-size: 16px; font-weight: 500; color: #6c757d; border-bottom: 2px solid transparent; cursor: pointer; transition: all 0.3s ease;">
                        <i class="fas fa-arrow-up" style="margin-right: 8px;"></i>出库管理
                    </button>
                    <button class="tab-btn" data-tab="lending" style="padding: 12px 24px; border: none; background: none; font-size: 16px; font-weight: 500; color: #6c757d; border-bottom: 2px solid transparent; cursor: pointer; transition: all 0.3s ease;">
                        <i class="fas fa-exchange-alt" style="margin-right: 8px;"></i>借出归还
                    </button>
                    <button class="tab-btn" data-tab="settings" style="padding: 12px 24px; border: none; background: none; font-size: 16px; font-weight: 500; color: #6c757d; border-bottom: 2px solid transparent; cursor: pointer; transition: all 0.3s ease;">
                        <i class="fas fa-cogs" style="margin-right: 8px;"></i>库存设置
                    </button>
                </div>

                <!-- 标签页内容 -->
                <div class="tab-content">
                    <!-- 订单需求模块 -->
                    <div class="module-container active" id="requests-module">
                        <!-- 内容由partsModule.js动态生成 -->
                    </div>

                    <!-- 入库管理模块 -->
                    <div class="module-container" id="inbound-module">
                        <!-- 内容由partsModule.js动态生成 -->
                    </div>

                    <!-- 出库管理模块 -->
                    <div class="module-container" id="outbound-module">
                        <!-- 内容由partsModule.js动态生成 -->
                    </div>

                    <!-- 借出归还模块 -->
                    <div class="module-container" id="lending-module">
                        <!-- 内容由partsModule.js动态生成 -->
                    </div>

                    <!-- 库存设置模块 -->
                    <div class="module-container" id="settings-module">
                        <!-- 内容由partsModule.js动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="database.js"></script>
    <script src="notification.js"></script>
    <script src="partsModule.js"></script>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 等待所有脚本加载完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 检查必要的函数是否存在
                if (!window.dbFunctions) {
                    throw new Error('数据库函数未加载');
                }

                if (!window.partsFunctions) {
                    throw new Error('配件库管理模块未加载');
                }

                // 初始化数据库
                await window.dbFunctions.initDB();

                // 初始化配件库管理模块
                await window.partsFunctions.init();

                console.log('配件库管理页面初始化完成');
            } catch (error) {
                console.error('初始化失败:', error);
                if (window.showNotification) {
                    showNotification('初始化失败', '系统初始化时出错: ' + error.message, 'danger');
                } else {
                    alert('初始化失败: ' + error.message);
                }
            }
        });

        // 标签页切换功能
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('tab-btn') || e.target.closest('.tab-btn')) {
                const tabBtn = e.target.classList.contains('tab-btn') ? e.target : e.target.closest('.tab-btn');
                const tabName = tabBtn.dataset.tab;
                
                // 移除所有标签页的active状态
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                    btn.style.color = '#6c757d';
                    btn.style.borderBottomColor = 'transparent';
                });
                
                document.querySelectorAll('.module-container').forEach(container => {
                    container.classList.remove('active');
                });
                
                // 激活当前标签页
                tabBtn.classList.add('active');
                tabBtn.style.color = '#4361ee';
                tabBtn.style.borderBottomColor = '#4361ee';
                
                const targetModule = document.getElementById(tabName + '-module');
                if (targetModule) {
                    targetModule.classList.add('active');
                }
                
                // 切换到对应模块
                if (window.partsFunctions && window.partsFunctions.switchTab) {
                    window.partsFunctions.switchTab(tabName);
                }
            }
        });
    </script>
</body>
</html>
