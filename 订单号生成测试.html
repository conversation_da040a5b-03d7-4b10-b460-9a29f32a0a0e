<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单号生成功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-section h2 {
            color: #555;
            margin-top: 0;
        }
        .test-input {
            margin-bottom: 15px;
        }
        .test-input label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .test-input input {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 200px;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .result-item {
            margin-bottom: 10px;
            padding: 8px;
            background-color: white;
            border-radius: 3px;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>订单号生成功能测试</h1>
        
        <div class="test-section">
            <h2>1. 拼音转换测试</h2>
            <p>测试销售顾问姓名转换为拼音首字母缩写的功能</p>
            <button class="test-button" onclick="testPinyinConversion()">运行拼音转换测试</button>
            <div id="pinyinResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>2. 订单号格式测试</h2>
            <p>测试订单号生成格式：YYMMDDXXX01（10位）</p>
            <div class="test-input">
                <label>销售顾问：</label>
                <input type="text" id="advisorName" value="张三" placeholder="请输入销售顾问姓名">
            </div>
            <div class="test-input">
                <label>订单日期：</label>
                <input type="date" id="orderDate" value="2025-07-26">
            </div>
            <button class="test-button" onclick="testOrderNumberFormat()">生成订单号测试</button>
            <div id="orderNumberResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>3. 批量测试</h2>
            <p>测试多个销售顾问的订单号生成</p>
            <button class="test-button" onclick="testBatchOrderNumbers()">运行批量测试</button>
            <div id="batchResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 复制orderModule.js中的chineseToPinyin函数
        function chineseToPinyin(chinese) {
            // 中文字符到拼音首字母的映射表（常见姓名字符）
            const pinyinFirstLetterMap = {
                // 常见姓氏
                '张': 'Z', '王': 'W', '李': 'L', '赵': 'Z', '陈': 'C', '刘': 'L', '杨': 'Y', '黄': 'H',
                '周': 'Z', '吴': 'W', '徐': 'X', '孙': 'S', '马': 'M', '朱': 'Z', '胡': 'H', '郭': 'G',
                '何': 'H', '高': 'G', '林': 'L', '罗': 'L', '郑': 'Z', '梁': 'L', '谢': 'X', '宋': 'S',
                '唐': 'T', '许': 'X', '韩': 'H', '冯': 'F', '邓': 'D', '曹': 'C', '彭': 'P', '曾': 'Z',
                '萧': 'X', '田': 'T', '董': 'D', '袁': 'Y', '潘': 'P', '于': 'Y', '蒋': 'J', '蔡': 'C',
                '余': 'Y', '杜': 'D', '叶': 'Y', '程': 'C', '魏': 'W', '苏': 'S', '吕': 'L', '丁': 'D',
                '任': 'R', '沈': 'S', '姚': 'Y', '卢': 'L', '姜': 'J', '崔': 'C', '钟': 'Z', '谭': 'T',
                '陆': 'L', '汪': 'W', '范': 'F', '金': 'J', '石': 'S', '廖': 'L', '贾': 'J', '夏': 'X',
                '韦': 'W', '付': 'F', '方': 'F', '白': 'B', '邹': 'Z', '孟': 'M', '熊': 'X', '秦': 'Q',
                '邱': 'Q', '江': 'J', '尹': 'Y', '薛': 'X', '闫': 'Y', '段': 'D', '雷': 'L', '侯': 'H',
                '龙': 'L', '史': 'S', '陶': 'T', '黎': 'L', '贺': 'H', '顾': 'G', '毛': 'M', '郝': 'H',
                '龚': 'G', '邵': 'S', '万': 'W', '钱': 'Q', '严': 'Y', '覃': 'Q', '武': 'W', '戴': 'D',
                '莫': 'M', '孔': 'K', '向': 'X', '汤': 'T', '常': 'C', '温': 'W', '康': 'K', '施': 'S',
                
                // 常见名字字符
                '一': 'Y', '二': 'E', '三': 'S', '四': 'S', '五': 'W', '六': 'L', '七': 'Q', '八': 'B', '九': 'J', '十': 'S',
                '伟': 'W', '芳': 'F', '娜': 'N', '敏': 'M', '静': 'J', '丽': 'L', '强': 'Q', '磊': 'L', '军': 'J', '洋': 'Y',
                '勇': 'Y', '艳': 'Y', '杰': 'J', '涛': 'T', '明': 'M', '超': 'C', '秀': 'X', '英': 'Y', '华': 'H', '慧': 'H',
                '嘉': 'J', '欣': 'X', '雨': 'Y', '雪': 'X', '梅': 'M', '美': 'M', '兰': 'L', '竹': 'Z', '菊': 'J', '松': 'S', '柏': 'B',
                '建': 'J', '国': 'G', '民': 'M', '文': 'W', '武': 'W', '德': 'D', '福': 'F', '贵': 'G', '富': 'F', '康': 'K',
                '安': 'A', '平': 'P', '和': 'H', '顺': 'S', '祥': 'X', '瑞': 'R', '吉': 'J', '利': 'L', '成': 'C', '功': 'G',
                '东': 'D', '南': 'N', '西': 'X', '北': 'B', '中': 'Z', '正': 'Z', '天': 'T', '地': 'D', '人': 'R', '心': 'X',
                '志': 'Z', '远': 'Y', '达': 'D', '通': 'T', '广': 'G', '深': 'S', '长': 'C', '高': 'G', '大': 'D', '小': 'X',
                '新': 'X', '老': 'L', '青': 'Q', '红': 'H', '绿': 'L', '蓝': 'L', '紫': 'Z', '黑': 'H', '白': 'B', '灰': 'H',
                '金': 'J', '银': 'Y', '铜': 'T', '铁': 'T', '钢': 'G', '玉': 'Y', '珠': 'Z', '宝': 'B', '石': 'S', '山': 'S',
                '水': 'S', '火': 'H', '土': 'T', '木': 'M', '花': 'H', '草': 'C', '树': 'S', '林': 'L', '森': 'S', '江': 'J',
                '河': 'H', '湖': 'H', '海': 'H', '洋': 'Y', '波': 'B', '浪': 'L', '云': 'Y', '风': 'F', '雷': 'L', '电': 'D',
                '忍': 'R', '斌': 'B', '诚': 'C', '健': 'J', '洁': 'J', '晓': 'X', '瑜': 'Y', '佳': 'J', '颖': 'Y'
            };

            if (!chinese) return 'XXX';

            let result = '';
            // 处理姓名，通常取前3个字符（姓+名的前两个字）
            for (let i = 0; i < Math.min(chinese.length, 3); i++) {
                const char = chinese[i];
                if (pinyinFirstLetterMap[char]) {
                    result += pinyinFirstLetterMap[char];
                } else if (/[A-Za-z]/.test(char)) {
                    // 如果是英文字母，直接使用大写
                    result += char.toUpperCase();
                } else {
                    // 未知字符使用X代替
                    result += 'X';
                }
            }

            // 确保结果始终为3位，不足的用X填充
            return result.padEnd(3, 'X').substring(0, 3);
        }

        function generateOrderNumber(advisorName, orderDate) {
            const date = new Date(orderDate);
            const year = date.getFullYear().toString().slice(-2);
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const advisorCode = chineseToPinyin(advisorName);
            
            // 模拟序号（实际应用中从数据库获取）
            const sequence = '01';
            
            return `${year}${month}${day}${advisorCode}${sequence}`;
        }

        function testPinyinConversion() {
            const testNames = ['张三', '李四', '王五', '赵小明', '陈建国', '刘小红', '杨志强', '黄美丽'];
            const resultDiv = document.getElementById('pinyinResult');

            let html = '<h3>拼音转换测试结果：</h3><table><tr><th>姓名</th><th>实际结果</th><th>预期结果</th><th>状态</th><th>说明</th></tr>';

            const expectedResults = {
                '张三': 'ZSX',     // 张=Z, 三=S, +X填充
                '李四': 'LSX',     // 李=L, 四=S, +X填充
                '王五': 'WWX',     // 王=W, 五=W, +X填充
                '赵小明': 'ZXM',   // 赵=Z, 小=X, 明=M
                '陈建国': 'CJG',   // 陈=C, 建=J, 国=G
                '刘小红': 'LXH',   // 刘=L, 小=X, 红=H
                '杨志强': 'YZQ',   // 杨=Y, 志=Z, 强=Q
                '黄美丽': 'HML'    // 黄=H, 美=M, 丽=L
            };

            const explanations = {
                '张三': '张=Z, 三=S, 2字姓名+X填充',
                '李四': '李=L, 四=S, 2字姓名+X填充',
                '王五': '王=W, 五=W, 2字姓名+X填充',
                '赵小明': '赵=Z, 小=X, 明=M, 3字姓名',
                '陈建国': '陈=C, 建=J, 国=G, 3字姓名',
                '刘小红': '刘=L, 小=X, 红=H, 3字姓名',
                '杨志强': '杨=Y, 志=Z, 强=Q, 3字姓名',
                '黄美丽': '黄=H, 美=M, 丽=L, 3字姓名'
            };

            let allCorrect = true;
            testNames.forEach(name => {
                const result = chineseToPinyin(name);
                const expected = expectedResults[name] || 'XXX';
                const isCorrect = result === expected;
                const status = isCorrect ? '✓ 正确' : '✗ 错误';
                const statusClass = isCorrect ? 'success' : 'error';
                const explanation = explanations[name] || '';

                if (!isCorrect) allCorrect = false;

                html += `<tr class="${statusClass}">
                    <td>${name}</td>
                    <td><strong>${result}</strong></td>
                    <td>${expected}</td>
                    <td>${status}</td>
                    <td style="font-size: 12px;">${explanation}</td>
                </tr>`;
            });

            html += '</table>';

            if (allCorrect) {
                html += '<div class="result-item success" style="margin-top: 15px; padding: 10px; background-color: #d4edda; border-left: 4px solid #28a745;">✓ 所有测试通过！拼音转换功能正常工作。</div>';
            } else {
                html += '<div class="result-item error" style="margin-top: 15px; padding: 10px; background-color: #f8d7da; border-left: 4px solid #dc3545;">✗ 部分测试失败，需要检查字符映射表。</div>';
            }

            resultDiv.innerHTML = html;
            resultDiv.style.display = 'block';
        }

        function testOrderNumberFormat() {
            const advisorName = document.getElementById('advisorName').value;
            const orderDate = document.getElementById('orderDate').value;
            const resultDiv = document.getElementById('orderNumberResult');
            
            if (!advisorName || !orderDate) {
                resultDiv.innerHTML = '<p class="error">请输入销售顾问姓名和订单日期</p>';
                resultDiv.style.display = 'block';
                return;
            }
            
            const orderNumber = generateOrderNumber(advisorName, orderDate);
            const advisorCode = chineseToPinyin(advisorName);
            
            let html = '<h3>订单号生成结果：</h3>';
            html += `<div class="result-item"><strong>销售顾问：</strong> ${advisorName}</div>`;
            html += `<div class="result-item"><strong>拼音缩写：</strong> ${advisorCode}</div>`;
            html += `<div class="result-item"><strong>订单日期：</strong> ${orderDate}</div>`;
            html += `<div class="result-item"><strong>生成的订单号：</strong> <span style="font-size: 18px; color: #007bff; font-weight: bold;">${orderNumber}</span></div>`;
            html += `<div class="result-item"><strong>订单号长度：</strong> ${orderNumber.length}位 ${orderNumber.length === 10 ? '✓ 正确' : '✗ 错误'}</div>`;
            
            // 格式分析
            html += '<h4>格式分析：</h4>';
            html += `<div class="result-item">年份：${orderNumber.substring(0, 2)} (${orderDate.substring(2, 4)})</div>`;
            html += `<div class="result-item">月份：${orderNumber.substring(2, 4)} (${orderDate.substring(5, 7)})</div>`;
            html += `<div class="result-item">日期：${orderNumber.substring(4, 6)} (${orderDate.substring(8, 10)})</div>`;
            html += `<div class="result-item">销售顾问代码：${orderNumber.substring(6, 9)} (${advisorCode})</div>`;
            html += `<div class="result-item">序号：${orderNumber.substring(9, 11)} (01)</div>`;
            
            resultDiv.innerHTML = html;
            resultDiv.style.display = 'block';
        }

        function testBatchOrderNumbers() {
            const testData = [
                { name: '张三', date: '2025-07-26' },
                { name: '李四', date: '2025-07-26' },
                { name: '王五', date: '2025-07-26' },
                { name: '赵六', date: '2025-07-27' },
                { name: '陈七', date: '2025-07-27' }
            ];
            
            const resultDiv = document.getElementById('batchResult');
            
            let html = '<h3>批量订单号生成测试：</h3>';
            html += '<table><tr><th>销售顾问</th><th>日期</th><th>拼音缩写</th><th>订单号</th><th>长度</th></tr>';
            
            testData.forEach(data => {
                const orderNumber = generateOrderNumber(data.name, data.date);
                const advisorCode = chineseToPinyin(data.name);
                const lengthStatus = orderNumber.length === 10 ? '✓' : '✗';
                
                html += `<tr><td>${data.name}</td><td>${data.date}</td><td>${advisorCode}</td><td>${orderNumber}</td><td>${orderNumber.length} ${lengthStatus}</td></tr>`;
            });
            
            html += '</table>';
            html += '<p><strong>说明：</strong>实际系统中，同一天同一销售顾问的订单序号会自动递增（01, 02, 03...）</p>';
            
            resultDiv.innerHTML = html;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
