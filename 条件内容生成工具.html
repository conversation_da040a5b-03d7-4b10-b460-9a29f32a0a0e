<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>条件内容生成工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        textarea {
            width: 100%;
            height: 150px;
            margin: 10px 0;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>条件内容生成工具</h1>
    
    <div class="instructions">
        <h3>使用说明：</h3>
        <ol>
            <li>在下方输入区域粘贴或输入表格数据（第一列是"是/否"，第二列是内容）</li>
            <li>每行一条记录，列之间用逗号、制表符或其他分隔符分隔</li>
            <li>点击"生成结果"按钮</li>
            <li>结果将显示在下方表格中，并汇总在文本区域</li>
        </ol>
    </div>
    
    <div>
        <label for="inputData">输入数据：</label><br>
        <textarea id="inputData" placeholder="请输入数据，例如：
是,苹果
否,香蕉
是,橙子
是,葡萄"></textarea>
    </div>
    
    <div>
        <label for="delimiter">分隔符：</label>
        <select id="delimiter">
            <option value=",">逗号 ( , )</option>
            <option value="\t">制表符</option>
            <option value=";">分号 ( ; )</option>
            <option value="|">竖线 ( | )</option>
        </select>
    </div>
    
    <button onclick="processData()">生成结果</button>
    
    <div>
        <h3>结果表格：</h3>
        <table id="resultTable">
            <thead>
                <tr>
                    <th>第一列 (是/否)</th>
                    <th>第二列 (内容)</th>
                    <th>第三列 (生成内容)</th>
                </tr>
            </thead>
            <tbody id="tableBody">
                <!-- 结果将在这里显示 -->
            </tbody>
        </table>
    </div>
    
    <div>
        <label for="outputData">生成的内容：</label><br>
        <textarea id="outputData" readonly></textarea>
    </div>
    
    <script>
        function processData() {
            const inputData = document.getElementById('inputData').value;
            const delimiter = document.getElementById('delimiter').value;
            const tableBody = document.getElementById('tableBody');
            const outputData = document.getElementById('outputData');
            
            // 清空之前的结果
            tableBody.innerHTML = '';
            outputData.value = '';
            
            const lines = inputData.split('\n');
            let generatedContent = [];
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (line === '') continue;
                
                // 分割列
                const columns = line.split(new RegExp(delimiter));
                if (columns.length < 2) continue;
                
                const firstCol = columns[0].trim();
                const secondCol = columns[1].trim();
                let thirdCol = '';
                
                // 检查第一列是否为"是"
                if (firstCol.toLowerCase() === '是' || firstCol.toLowerCase() === 'yes') {
                    thirdCol = secondCol;
                    generatedContent.push(thirdCol);
                }
                
                // 创建表格行
                const row = document.createElement('tr');
                
                const cell1 = document.createElement('td');
                cell1.textContent = firstCol;
                row.appendChild(cell1);
                
                const cell2 = document.createElement('td');
                cell2.textContent = secondCol;
                row.appendChild(cell2);
                
                const cell3 = document.createElement('td');
                cell3.textContent = thirdCol;
                row.appendChild(cell3);
                
                tableBody.appendChild(row);
            }
            
            // 将生成的内容填入输出区域
            outputData.value = generatedContent.join('\n');
        }
    </script>
</body>
</html>