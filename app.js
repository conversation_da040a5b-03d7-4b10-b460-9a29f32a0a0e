// 主页专用脚本
document.addEventListener('DOMContentLoaded', async function() {
    // 显示通知
    const notification = document.querySelector('.notification');
    if (notification) {
        notification.classList.add('show');
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }
    
    // 初始化数据库
    try {
        await initDB();
        await seedDemoData();
        updateDbStatus();
        
        // 更新系统信息
        updateSystemInfo();
        
    } catch (error) {
        console.error("数据库初始化失败:", error);
        if (notification) {
            notification.querySelector('div > div:first-child').textContent = "数据库错误";
            notification.querySelector('div > div:last-child').textContent = error.message;
            notification.classList.add('show', 'danger');
        }
    }
});

// 更新系统信息
async function updateSystemInfo() {
    try {
        const customers = await getAllCustomers();
        const activeUsersElement = document.getElementById('active-users');
        const lastUpdateElement = document.getElementById('last-update');
        
        if (activeUsersElement) {
            activeUsersElement.textContent = '1'; // 当前只有一个用户
        }
        
        if (lastUpdateElement) {
            lastUpdateElement.textContent = new Date().toLocaleString('zh-CN');
        }
        
    } catch (error) {
        console.error('更新系统信息失败:', error);
    }
}

// 更新数据库状态
function updateDbStatus() {
    // 模拟数据库使用情况
    const usage = Math.floor(Math.random() * 50) + 10; // 10-60MB
    const usageElement = document.getElementById('db-usage');
    const progressElement = document.getElementById('db-progress');
    const syncElement = document.getElementById('last-sync');
    
    if (usageElement) {
        usageElement.textContent = `${usage} MB / 500 MB`;
    }
    
    if (progressElement) {
        progressElement.style.width = `${(usage / 500) * 100}%`;
    }
    
    if (syncElement) {
        syncElement.textContent = new Date().toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }
}

